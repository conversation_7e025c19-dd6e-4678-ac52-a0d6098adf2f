# 🛠 Data Management Guide

Panduan lengkap untuk menghapus, <PERSON><PERSON><PERSON>, dan mengelola data dalam aplikasi Talent Mapping Assessment.

## 📋 Metode Pengelolaan Data

### 1. **🎯 Prisma Studio (Recommended untuk Pemula)**

Interface web yang user-friendly untuk mengelola data:

```bash
npm run db:studio
```

**Fitur:**
- ✅ GUI yang mudah digunakan
- ✅ Edit data langsung dalam tabel
- ✅ Hapus record individual
- ✅ Filter dan search
- ✅ Export data
- ✅ Aman untuk pemula

**Akses:** `http://localhost:5555`

### 2. **🧹 Script Pembersihan Interaktif**

Menu interaktif untuk operasi pembersihan umum:

```bash
npm run db:clean
```

**Opsi yang tersedia:**
- Lihat statistik database
- Hapus semua profiles
- Hapus semua assessments
- Hapus semua users
- Hapus data berdasarkan umur (hari)
- Hapus orphaned profiles

### 3. **⚡ Quick Clean**

Hapus semua data assessment dan profile dengan cepat:

```bash
npm run db:quick-clean
```

**⚠️ WARNING:** Ini akan menghapus SEMUA data assessment dan profile!

### 4. **🔧 Data Manager (Operasi Spesifik)**

Tool untuk operasi data yang lebih spesifik:

```bash
npm run db:manage
```

**Fitur:**
- 📊 Lihat assessment spesifik
- ✏️ Edit skor assessment
- 🗑️ Hapus assessment tertentu
- 📝 Edit data profile
- 🔍 Search berdasarkan tanggal
- 📋 List semua assessments
- 📊 Statistik database

### 5. **💻 SQL Runner (Advanced)**

Akses langsung ke database dengan SQL:

```bash
npm run db:sql
```

**Fitur:**
- 📊 Query predefined yang aman
- ⚠️ Operasi UPDATE/DELETE
- 💻 Custom SQL queries
- 📋 Lihat schema database

## 🎯 Skenario Penggunaan Umum

### **Scenario 1: Hapus Semua Data Test**

```bash
# Metode 1: Quick clean
npm run db:quick-clean

# Metode 2: Interactive clean
npm run db:clean
# Pilih opsi 1 (hapus semua profiles) dan 2 (hapus semua assessments)
```

### **Scenario 2: Hapus Assessment Tertentu**

```bash
# Metode 1: Prisma Studio
npm run db:studio
# Buka tabel assessments, cari ID, klik delete

# Metode 2: Data Manager
npm run db:manage
# Pilih opsi 3, masukkan ID assessment
```

### **Scenario 3: Edit Skor Assessment**

```bash
# Metode 1: Prisma Studio
npm run db:studio
# Edit langsung di tabel assessments

# Metode 2: Data Manager
npm run db:manage
# Pilih opsi 2, masukkan ID dan skor baru
```

### **Scenario 4: Lihat Data Tertentu**

```bash
# Metode 1: Data Manager
npm run db:manage
# Pilih opsi 1 untuk lihat assessment spesifik

# Metode 2: SQL Runner
npm run db:sql
# Pilih query predefined atau buat custom query
```

### **Scenario 5: Hapus Data Lama**

```bash
# Metode 1: Interactive Clean
npm run db:clean
# Pilih opsi 5, masukkan jumlah hari

# Metode 2: SQL Runner
npm run db:sql
# Pilih operasi predefined untuk hapus data lama
```

## ⚠️ Peringatan Keamanan

### **Backup Data Sebelum Operasi Besar**

```bash
# Backup otomatis dibuat oleh script clean-database.js
# File backup: scripts/backup-profiles.json
```

### **Operasi yang Tidak Bisa Di-Undo**

- ❌ DELETE operations
- ❌ DROP TABLE
- ❌ Quick clean
- ✅ UPDATE operations (bisa diubah lagi)

### **Best Practices**

1. **Selalu backup data penting**
2. **Test di development environment dulu**
3. **Gunakan Prisma Studio untuk operasi kecil**
4. **Gunakan script untuk operasi batch**
5. **Verifikasi hasil setelah operasi**

## 📊 Struktur Database

### **Tabel Utama:**

```sql
-- Assessments: Menyimpan skor tes
assessments (
  id, userId, createdAt, updatedAt,
  riasecR, riasecI, riasecA, riasecS, riasecE, riasecC,
  oceanO, oceanC, oceanE, oceanA, oceanN
)

-- Profiles: Menyimpan hasil analisis AI
profiles (
  id, assessmentId, createdAt, updatedAt,
  profileTitle, profileDescription, strengths,
  careerSuggestions, workEnvironment, developmentAreas,
  personalityInsights, careerFit, generatedAt, aiModel
)

-- Users: Data user (opsional)
users (
  id, email, name, createdAt, updatedAt
)
```

### **Relasi:**
- `profiles.assessmentId` → `assessments.id` (1:1)
- `assessments.userId` → `users.id` (N:1, optional)

## 🔍 Query SQL Berguna

### **Lihat Semua Assessment dengan Status Profile:**

```sql
SELECT a.id, a.createdAt, 
       CASE WHEN p.id IS NOT NULL THEN 'Yes' ELSE 'No' END as hasProfile,
       p.profileTitle
FROM assessments a 
LEFT JOIN profiles p ON a.id = p.assessmentId 
ORDER BY a.createdAt DESC;
```

### **Hapus Assessment Lama (30 hari):**

```sql
DELETE FROM assessments 
WHERE createdAt < datetime('now', '-30 days');
```

### **Update Skor Spesifik:**

```sql
UPDATE assessments 
SET riasecR = 25, updatedAt = datetime('now') 
WHERE id = 'assessment_id_here';
```

### **Lihat Statistik Skor:**

```sql
SELECT 
  AVG(riasecR) as avg_R, AVG(riasecI) as avg_I,
  AVG(oceanO) as avg_O, AVG(oceanC) as avg_C
FROM assessments;
```

## 🚀 Tips dan Trik

### **1. Monitoring Database Size**

```bash
# Lihat ukuran file database
ls -lh prisma/dev.db

# Lihat jumlah records
npm run db:manage
# Pilih opsi 8 untuk statistik
```

### **2. Export Data untuk Backup**

```bash
# Menggunakan Prisma Studio
npm run db:studio
# Klik Export pada setiap tabel

# Atau menggunakan SQLite command
sqlite3 prisma/dev.db ".dump" > backup.sql
```

### **3. Import Data dari Backup**

```bash
# Restore dari SQL dump
sqlite3 prisma/dev.db < backup.sql
```

### **4. Reset Database Completely**

```bash
# Reset schema dan data
npm run db:reset

# Regenerate Prisma client
npm run db:generate
```

## 🆘 Troubleshooting

### **Error: Database locked**
```bash
# Stop development server
# Close Prisma Studio
# Try operation again
```

### **Error: Foreign key constraint**
```bash
# Hapus profiles dulu, baru assessments
# Atau gunakan CASCADE delete
```

### **Error: Prisma client out of sync**
```bash
npm run db:generate
```

## 📞 Support

Jika mengalami masalah:

1. **Check logs** di terminal
2. **Backup data** sebelum troubleshooting
3. **Restart development server**
4. **Regenerate Prisma client**
5. **Check database file permissions**

---

**⚠️ INGAT:** Selalu backup data penting sebelum melakukan operasi penghapusan atau perubahan besar!
