const { PrismaClient } = require('../lib/generated/prisma');

const prisma = new PrismaClient();

async function populateResultIds() {
  console.log('🔄 Populating resultId for existing assessments...');
  
  try {
    // Find assessments without resultId
    const assessments = await prisma.assessment.findMany({
      where: { resultId: null },
    });

    console.log(`📊 Found ${assessments.length} assessments without resultId`);

    if (assessments.length === 0) {
      console.log('✅ All assessments already have resultId');
      return;
    }

    // Update each assessment with a resultId
    for (const assessment of assessments) {
      await prisma.assessment.update({
        where: { id: assessment.id },
        data: { resultId: assessment.id }, // Use existing ID as resultId for backward compatibility
      });
      console.log(`✅ Updated assessment ${assessment.id} with resultId`);
    }

    console.log(`🎉 Successfully populated resultId for ${assessments.length} assessments`);
  } catch (error) {
    console.error('❌ Error populating resultIds:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
populateResultIds();
