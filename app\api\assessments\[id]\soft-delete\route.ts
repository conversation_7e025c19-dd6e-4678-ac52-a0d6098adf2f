import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
} from '@/lib/api-utils';
import { enhancedDb } from '@/lib/server/enhanced-database';
import { withMiddleware } from '@/lib/server/middleware';
import { 
  createSoftDeleteSuccessResponse, 
  createSoftDeleteErrorResponse 
} from '@/lib/utils/soft-delete';

interface SoftDeleteParams {
  params: {
    id: string;
  };
}

// DELETE - Soft delete assessment
async function deleteHandler(
  request: NextRequest,
  { params }: SoftDeleteParams
) {
  const assessmentId = params.id;

  // Validate assessment ID
  if (!assessmentId || assessmentId.length < 10) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid assessment ID',
      400
    );
  }

  try {
    // Check if assessment exists and is not already deleted
    const existingAssessment = await enhancedDb.getAssessment(assessmentId, false);
    
    if (!existingAssessment) {
      return createErrorResponse(
        'NOT_FOUND',
        'Assessment not found or already deleted',
        404
      );
    }

    // Get user ID for audit trail (if available)
    const userId = request.headers.get('x-user-id') || undefined;

    // Perform soft delete
    await enhancedDb.softDeleteAssessment(assessmentId, userId);

    const response = createSoftDeleteSuccessResponse('deleted', 'Assessment');
    return createSuccessResponse(response);

  } catch (error) {
    console.error('Soft delete assessment error:', error);
    
    const errorResponse = createSoftDeleteErrorResponse(
      'delete',
      'assessment',
      error instanceof Error ? error.message : 'Unknown error'
    );
    
    return createErrorResponse(
      'INTERNAL_ERROR',
      errorResponse.message,
      500
    );
  }
}

// PUT - Restore soft deleted assessment
async function putHandler(
  request: NextRequest,
  { params }: SoftDeleteParams
) {
  const assessmentId = params.id;

  // Validate assessment ID
  if (!assessmentId || assessmentId.length < 10) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid assessment ID',
      400
    );
  }

  try {
    // Check if assessment exists and is deleted
    const existingAssessment = await enhancedDb.getAssessment(assessmentId, true);
    
    if (!existingAssessment) {
      return createErrorResponse(
        'NOT_FOUND',
        'Assessment not found',
        404
      );
    }

    if (!existingAssessment.deletedAt) {
      return createErrorResponse(
        'VALIDATION_ERROR',
        'Assessment is not deleted',
        400
      );
    }

    // Get user ID for audit trail (if available)
    const userId = request.headers.get('x-user-id') || undefined;

    // Restore assessment (remove deletedAt)
    await enhancedDb.restoreAssessment(assessmentId, userId);

    const response = createSoftDeleteSuccessResponse('restored', 'Assessment');
    return createSuccessResponse(response);

  } catch (error) {
    console.error('Restore assessment error:', error);
    
    const errorResponse = createSoftDeleteErrorResponse(
      'restore',
      'assessment',
      error instanceof Error ? error.message : 'Unknown error'
    );
    
    return createErrorResponse(
      'INTERNAL_ERROR',
      errorResponse.message,
      500
    );
  }
}

// GET - Get soft delete status
async function getHandler(
  request: NextRequest,
  { params }: SoftDeleteParams
) {
  const assessmentId = params.id;

  // Validate assessment ID
  if (!assessmentId || assessmentId.length < 10) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid assessment ID',
      400
    );
  }

  try {
    // Get assessment including deleted ones
    const assessment = await enhancedDb.getAssessment(assessmentId, true);
    
    if (!assessment) {
      return createErrorResponse(
        'NOT_FOUND',
        'Assessment not found',
        404
      );
    }

    const response = {
      id: assessment.id,
      isDeleted: !!assessment.deletedAt,
      deletedAt: assessment.deletedAt?.toISOString() || null,
      createdAt: assessment.createdAt.toISOString(),
      updatedAt: assessment.updatedAt.toISOString(),
    };

    return createSuccessResponse(response);

  } catch (error) {
    console.error('Get soft delete status error:', error);
    
    return createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to get assessment status',
      500
    );
  }
}

export const DELETE = withMiddleware(deleteHandler);
export const PUT = withMiddleware(putHandler);
export const GET = withMiddleware(getHandler);
