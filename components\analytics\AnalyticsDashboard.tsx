'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from 'recharts';
import {
  TrendingUp,
  Users,
  FileText,
  Zap,
  Clock,
  Database,
  Activity,
  AlertCircle,
  BarChart3,
  Monitor,
  Download,
} from 'lucide-react';
import { PerformanceMetrics } from './PerformanceMetrics';
import { UserBehaviorAnalytics } from './UserBehaviorAnalytics';
import { SystemHealthMonitor } from './SystemHealthMonitor';
import { ExportAnalytics } from './ExportAnalytics';

interface AnalyticsData {
  overview: {
    totalAssessments: number;
    totalProfiles: number;
    totalUsers: number;
    cacheHitRate: number;
  };
  dailyStats: Array<{
    date: string;
    assessments: number;
    profiles: number;
    avgRiasecScores: {
      R: number;
      I: number;
      A: number;
      S: number;
      E: number;
      C: number;
    };
    avgOceanScores: {
      O: number;
      C: number;
      E: number;
      A: number;
      N: number;
    };
  }>;
  cacheStats: {
    totalEntries: number;
    totalHits: number;
    hitRate: number;
    oldestEntry: string | null;
    newestEntry: string | null;
  };
  performanceMetrics: {
    totalRequests: number;
    cacheHits: number;
    cacheMisses: number;
    averageResponseTime: number;
    errorCount: number;
  };
}

interface AnalyticsDashboardProps {
  className?: string;
}

export function AnalyticsDashboard({ className = '' }: AnalyticsDashboardProps) {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState(30);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange]);

  const fetchAnalytics = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/analytics?days=${dateRange}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const result = await response.json();
      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      month: 'short',
      day: 'numeric',
    });
  };

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    color, 
    subtitle 
  }: {
    title: string;
    value: string | number;
    icon: React.ElementType;
    color: string;
    subtitle?: string;
  }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center py-12 ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading analytics...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-6 ${className}`}>
        <div className="flex items-center">
          <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
          <span className="text-red-800">Error loading analytics: {error}</span>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className={`text-center py-12 text-gray-500 ${className}`}>
        No analytics data available
      </div>
    );
  }

  // Prepare chart data
  const dailyChartData = data.dailyStats.map(day => ({
    date: formatDate(day.date),
    assessments: day.assessments,
    profiles: day.profiles,
  }));

  const riasecChartData = data.dailyStats.length > 0 ? [
    { name: 'Realistic', value: data.dailyStats[data.dailyStats.length - 1].avgRiasecScores.R },
    { name: 'Investigative', value: data.dailyStats[data.dailyStats.length - 1].avgRiasecScores.I },
    { name: 'Artistic', value: data.dailyStats[data.dailyStats.length - 1].avgRiasecScores.A },
    { name: 'Social', value: data.dailyStats[data.dailyStats.length - 1].avgRiasecScores.S },
    { name: 'Enterprising', value: data.dailyStats[data.dailyStats.length - 1].avgRiasecScores.E },
    { name: 'Conventional', value: data.dailyStats[data.dailyStats.length - 1].avgRiasecScores.C },
  ] : [];

  const oceanChartData = data.dailyStats.length > 0 ? [
    { name: 'Openness', value: data.dailyStats[data.dailyStats.length - 1].avgOceanScores.O },
    { name: 'Conscientiousness', value: data.dailyStats[data.dailyStats.length - 1].avgOceanScores.C },
    { name: 'Extraversion', value: data.dailyStats[data.dailyStats.length - 1].avgOceanScores.E },
    { name: 'Agreeableness', value: data.dailyStats[data.dailyStats.length - 1].avgOceanScores.A },
    { name: 'Neuroticism', value: data.dailyStats[data.dailyStats.length - 1].avgOceanScores.N },
  ] : [];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  const tabs = [
    { id: 'overview', name: 'Overview', icon: BarChart3 },
    { id: 'performance', name: 'Performance', icon: Activity },
    { id: 'users', name: 'User Behavior', icon: Users },
    { id: 'system', name: 'System Health', icon: Monitor },
    { id: 'export', name: 'Export', icon: Download },
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
        <div className="flex items-center space-x-2">
          <label className="text-sm text-gray-600">Date Range:</label>
          <select
            value={dateRange}
            onChange={(e) => setDateRange(parseInt(e.target.value))}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={7}>Last 7 days</option>
            <option value={30}>Last 30 days</option>
            <option value={90}>Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Assessments"
          value={data.overview.totalAssessments.toLocaleString()}
          icon={FileText}
          color="bg-blue-500"
        />
        <StatCard
          title="Total Profiles"
          value={data.overview.totalProfiles.toLocaleString()}
          icon={Users}
          color="bg-green-500"
        />
        <StatCard
          title="Total Users"
          value={data.overview.totalUsers.toLocaleString()}
          icon={Users}
          color="bg-purple-500"
        />
        <StatCard
          title="Cache Hit Rate"
          value={`${data.overview.cacheHitRate.toFixed(1)}%`}
          icon={Zap}
          color="bg-orange-500"
        />
      </div>

      {/* Daily Activity Chart */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Activity</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={dailyChartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="assessments" 
              stroke="#3B82F6" 
              strokeWidth={2}
              name="Assessments"
            />
            <Line 
              type="monotone" 
              dataKey="profiles" 
              stroke="#10B981" 
              strokeWidth={2}
              name="Profiles Generated"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Score Distribution Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* RIASEC Scores */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Average RIASEC Scores</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={riasecChartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis domain={[0, 30]} />
              <Tooltip />
              <Bar dataKey="value" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* OCEAN Scores */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Average OCEAN Scores</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={oceanChartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value }) => `${name}: ${value.toFixed(1)}`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {oceanChartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cache Performance */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Database className="w-5 h-5 mr-2" />
            Cache Performance
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Entries:</span>
              <span className="font-semibold">{data.cacheStats.totalEntries.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Total Hits:</span>
              <span className="font-semibold">{data.cacheStats.totalHits.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Hit Rate:</span>
              <span className="font-semibold text-green-600">{data.cacheStats.hitRate.toFixed(1)}%</span>
            </div>
          </div>
        </div>

        {/* System Performance */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            System Performance
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Requests:</span>
              <span className="font-semibold">{data.performanceMetrics.totalRequests.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Avg Response Time:</span>
              <span className="font-semibold">{data.performanceMetrics.averageResponseTime}ms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Error Count:</span>
              <span className={`font-semibold ${data.performanceMetrics.errorCount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {data.performanceMetrics.errorCount}
              </span>
            </div>
          </div>
        </div>
      </div>
          </div>
        )}

        {activeTab === 'performance' && (
          <PerformanceMetrics />
        )}

        {activeTab === 'users' && (
          <UserBehaviorAnalytics />
        )}

        {activeTab === 'system' && (
          <SystemHealthMonitor />
        )}

        {activeTab === 'export' && (
          <ExportAnalytics />
        )}
      </div>
    </div>
  );
}
