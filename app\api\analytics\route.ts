import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
} from '@/lib/api-utils';
import { enhancedDb } from '@/lib/server/enhanced-database';
import { withMiddleware } from '@/lib/server/middleware';

interface AnalyticsResponse {
  overview: {
    totalAssessments: number;
    totalProfiles: number;
    totalUsers: number;
    cacheHitRate: number;
  };
  dailyStats: Array<{
    date: string;
    assessments: number;
    profiles: number;
    avgRiasecScores: {
      R: number;
      I: number;
      A: number;
      S: number;
      E: number;
      C: number;
    };
    avgOceanScores: {
      O: number;
      C: number;
      E: number;
      A: number;
      N: number;
    };
  }>;
  cacheStats: {
    totalEntries: number;
    totalHits: number;
    hitRate: number;
    oldestEntry: string | null;
    newestEntry: string | null;
  };
  performanceMetrics: {
    totalRequests: number;
    cacheHits: number;
    cacheMisses: number;
    averageResponseTime: number;
    errorCount: number;
  };
}

// GET - Get analytics data
async function getHandler(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  // Date range parameters
  const startDate = searchParams.get('startDate');
  const endDate = searchParams.get('endDate');
  const days = parseInt(searchParams.get('days') || '30');

  try {
    // Calculate date range
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - (days * 24 * 60 * 60 * 1000));

    // Get overview statistics
    const [totalAssessments, totalProfiles, totalUsers] = await Promise.all([
      enhancedDb.getTotalAssessments(),
      enhancedDb.getTotalProfiles(),
      enhancedDb.getTotalUsers(),
    ]);

    // Get daily analytics
    const dailyAnalytics = await enhancedDb.getDailyAnalytics(start, end);

    // Get cache statistics
    const cacheStats = await enhancedDb.getCacheStats();

    // Get performance metrics from Gemini service
    const { EnhancedGeminiProfileService } = await import('@/lib/server/enhanced-gemini-service');
    const geminiService = EnhancedGeminiProfileService.getInstance();
    const performanceMetrics = geminiService.getMetrics();
    const geminiCacheStats = await geminiService.getCacheStats();

    // Transform daily analytics
    const transformedDailyStats = dailyAnalytics.map(day => ({
      date: day.date.toISOString().split('T')[0],
      assessments: day.totalAssessments,
      profiles: day.totalProfilesGenerated,
      avgRiasecScores: {
        R: day.avgRiasecR || 0,
        I: day.avgRiasecI || 0,
        A: day.avgRiasecA || 0,
        S: day.avgRiasecS || 0,
        E: day.avgRiasecE || 0,
        C: day.avgRiasecC || 0,
      },
      avgOceanScores: {
        O: day.avgOceanO || 0,
        C: day.avgOceanC || 0,
        E: day.avgOceanE || 0,
        A: day.avgOceanA || 0,
        N: day.avgOceanN || 0,
      },
    }));

    const response: AnalyticsResponse = {
      overview: {
        totalAssessments,
        totalProfiles,
        totalUsers,
        cacheHitRate: geminiCacheStats.hitRate || 0,
      },
      dailyStats: transformedDailyStats,
      cacheStats: {
        totalEntries: cacheStats.totalEntries,
        totalHits: cacheStats.totalHits,
        hitRate: geminiCacheStats.hitRate || 0,
        oldestEntry: cacheStats.oldestEntry?.toISOString() || null,
        newestEntry: cacheStats.newestEntry?.toISOString() || null,
      },
      performanceMetrics: {
        totalRequests: performanceMetrics.totalRequests,
        cacheHits: performanceMetrics.cacheHits,
        cacheMisses: performanceMetrics.cacheMisses,
        averageResponseTime: Math.round(performanceMetrics.averageResponseTime),
        errorCount: performanceMetrics.errorCount,
      },
    };

    return createSuccessResponse(response);

  } catch (error) {
    console.error('Analytics API error:', error);
    
    return createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to retrieve analytics data',
      500
    );
  }
}

// POST - Update daily analytics (for manual trigger)
async function postHandler(request: NextRequest) {
  try {
    const body = await request.json();
    const { date } = body;

    const targetDate = date ? new Date(date) : new Date();
    
    // Update analytics for the specified date
    const analytics = await enhancedDb.updateDailyAnalytics(targetDate);

    const response = {
      message: 'Daily analytics updated successfully',
      date: targetDate.toISOString().split('T')[0],
      analytics: {
        totalAssessments: analytics.totalAssessments,
        totalProfilesGenerated: analytics.totalProfilesGenerated,
      },
    };

    return createSuccessResponse(response);

  } catch (error) {
    console.error('Update analytics error:', error);
    
    return createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to update analytics',
      500
    );
  }
}

export const GET = withMiddleware(getHandler);
export const POST = withMiddleware(postHandler);
