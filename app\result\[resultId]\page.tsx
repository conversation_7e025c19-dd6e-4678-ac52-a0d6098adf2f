"use client";

import { useParams } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';
import Link from 'next/link';
import { resultsApi } from '@/lib/client/apiService';
import { ResultsResponse } from '@/lib/api-types';
import { getScoreLevel } from '@/lib/profileStore';
import RiasecChart from '@/components/RiasecChart';
import OceanChart from '@/components/OceanChart';
import ProfileSummary from '@/components/ProfileSummary';

function ResultContent() {
  const params = useParams();
  const resultId = params.resultId as string;
  
  const [resultsData, setResultsData] = useState<ResultsResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    if (!resultId) {
      setError('ID hasil tidak valid');
      setIsLoading(false);
      return;
    }

    const fetchResults = async () => {
      try {
        setIsLoading(true);
        const results = await resultsApi.get(resultId);
        setResultsData(results);
      } catch (error) {
        console.error('Error fetching results:', error);
        setError('Gagal memuat hasil tes. Silakan coba lagi.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  }, [resultId]);

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center p-4">
        <div className="max-w-md mx-auto text-center bg-white rounded-2xl shadow-xl p-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Error</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link 
            href="/"
            className="inline-block bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
          >
            Kembali ke Beranda
          </Link>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memuat hasil tes...</p>
        </div>
      </div>
    );
  }

  if (!resultsData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
        <div className="max-w-md mx-auto text-center bg-white rounded-2xl shadow-xl p-8">
          <div className="text-gray-500 text-6xl mb-4">📄</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Hasil Tidak Ditemukan</h1>
          <p className="text-gray-600 mb-6">Hasil tes dengan ID tersebut tidak ditemukan.</p>
          <Link 
            href="/"
            className="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
          >
            Kembali ke Beranda
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            Hasil Analisis Kepribadian & Minat Karir
          </h1>
          <p className="text-lg text-gray-600">
            Berikut adalah profil lengkap Anda berdasarkan analisis RIASEC dan Big Five (OCEAN)
          </p>
          <div className="mt-4 text-sm text-gray-500">
            ID Hasil: {resultId}
          </div>
        </div>

        {/* Profile Summary Section */}
        <ProfileSummary
          combinedProfile={{
            riasecData: {
              scores: resultsData.assessment.riasecScores,
              dominantTypes: Object.entries(resultsData.assessment.riasecScores)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 3)
                .map(([type]) => type as any),
              level: getScoreLevel(Math.max(...Object.values(resultsData.assessment.riasecScores))).level
            },
            oceanData: {
              scores: resultsData.assessment.oceanScores,
              traits: Object.entries(resultsData.assessment.oceanScores).map(([trait, score]) => ({
                trait: trait as any,
                name: trait === 'O' ? 'Openness' : trait === 'C' ? 'Conscientiousness' : trait === 'E' ? 'Extraversion' : trait === 'A' ? 'Agreeableness' : 'Neuroticism',
                score,
                level: (score > 20 ? 'Tinggi' : score > 15 ? 'Sedang' : 'Rendah') as 'Rendah' | 'Sedang' | 'Tinggi',
                description: `${trait} level ${score > 20 ? 'tinggi' : score > 15 ? 'sedang' : 'rendah'}`
              })),
              personalityType: 'Balanced'
            },
            profileTitle: resultsData.profile.profileTitle,
            profileDescription: resultsData.profile.profileDescription,
            strengths: resultsData.profile.strengths,
            careerSuggestions: resultsData.profile.careerSuggestions,
            workEnvironment: resultsData.profile.workEnvironment,
            developmentAreas: resultsData.profile.developmentAreas,
            personalityInsights: resultsData.profile.personalityInsights,
            careerFit: resultsData.profile.careerFit
          }}
          isCombinedMode={true}
        />

        {/* RIASEC Chart Section */}
        <RiasecChart scores={resultsData.assessment.riasecScores} />

        {/* OCEAN Chart Section */}
        <OceanChart scores={resultsData.assessment.oceanScores} />

        {/* Actions */}
        <div className="text-center">
          <div className="space-x-4">
            <Link 
              href="/"
              className="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
            >
              Kembali ke Beranda
            </Link>
            <Link 
              href="/test"
              className="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
            >
              Ulangi Tes
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ResultPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memuat hasil tes...</p>
        </div>
      </div>
    }>
      <ResultContent />
    </Suspense>
  );
}
