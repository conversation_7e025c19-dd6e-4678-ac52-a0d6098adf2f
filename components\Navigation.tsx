'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/store/authStore';
import UserMenu from './auth/UserMenu';
import AuthModal from './auth/AuthModal';

export default function Navigation() {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const { user, isAuthenticated } = useAuth();

  const handleShowLogin = () => {
    setAuthMode('login');
    setShowAuthModal(true);
  };

  const handleShowRegister = () => {
    setAuthMode('register');
    setShowAuthModal(true);
  };

  return (
    <>
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">A</span>
              </div>
              <span className="font-semibold text-gray-900">ATMA</span>
            </Link>

            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-6">
              <Link
                href="/"
                className="text-gray-600 hover:text-gray-900 text-sm font-medium"
              >
                Beranda
              </Link>
              <Link
                href="/test"
                className="text-gray-600 hover:text-gray-900 text-sm font-medium"
              >
                Mulai Tes
              </Link>
              <Link
                href="/demo-visualization"
                className="text-gray-600 hover:text-gray-900 text-sm font-medium"
              >
                Demo
              </Link>
              {isAuthenticated && (
                <Link
                  href="/dashboard"
                  className="text-gray-600 hover:text-gray-900 text-sm font-medium"
                >
                  Dashboard
                </Link>
              )}
            </div>

            {/* Auth Section */}
            <div className="flex items-center space-x-4">
              {isAuthenticated ? (
                <UserMenu />
              ) : (
                <div className="flex items-center space-x-3">
                  <button
                    onClick={handleShowLogin}
                    className="text-gray-600 hover:text-gray-900 text-sm font-medium"
                  >
                    Masuk
                  </button>
                  <button
                    onClick={handleShowRegister}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium px-4 py-2 rounded-lg transition-colors"
                  >
                    Daftar
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden border-t border-gray-200">
          <div className="px-4 py-3 space-y-2">
            <Link
              href="/"
              className="block text-gray-600 hover:text-gray-900 text-sm font-medium py-1"
            >
              Beranda
            </Link>
            <Link
              href="/test"
              className="block text-gray-600 hover:text-gray-900 text-sm font-medium py-1"
            >
              Mulai Tes
            </Link>
            <Link
              href="/demo-visualization"
              className="block text-gray-600 hover:text-gray-900 text-sm font-medium py-1"
            >
              Demo
            </Link>
            {isAuthenticated && (
              <Link
                href="/dashboard"
                className="block text-gray-600 hover:text-gray-900 text-sm font-medium py-1"
              >
                Dashboard
              </Link>
            )}
          </div>
        </div>
      </nav>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        defaultMode={authMode}
      />
    </>
  );
}
