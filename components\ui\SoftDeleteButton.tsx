'use client';

import React, { useState } from 'react';
import { Trash2, Rota<PERSON><PERSON>cw, AlertTriangle } from 'lucide-react';
import { useSoftDelete } from '@/lib/hooks/useSoftDelete';
import {
  getSoftDeleteConfirmationMessage,
  getRestoreConfirmationMessage,
  isSoftDeleted,
  SoftDeletable
} from '@/lib/utils/soft-delete';

interface SoftDeleteButtonProps {
  item: SoftDeletable;
  baseUrl: string;
  itemType: string;
  itemName?: string;
  onSuccess?: (action: 'deleted' | 'restored') => void;
  onError?: (error: string) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'button' | 'icon' | 'text';
  showConfirmation?: boolean;
}

export function SoftDeleteButton({
  item,
  baseUrl,
  itemType,
  itemName,
  onSuccess,
  onError,
  className = '',
  size = 'md',
  variant = 'button',
  showConfirmation = true,
}: SoftDeleteButtonProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<'delete' | 'restore' | null>(null);

  const { isLoading, softDelete, restore } = useSoftDelete(baseUrl, {
    onSuccess: (action) => {
      setShowConfirmDialog(false);
      setPendingAction(null);
      onSuccess?.(action);
    },
    onError: (error) => {
      setShowConfirmDialog(false);
      setPendingAction(null);
      onError?.(error);
    },
  });

  const isDeleted = isSoftDeleted(item);

  const handleAction = async (action: 'delete' | 'restore') => {
    if (showConfirmation) {
      setPendingAction(action);
      setShowConfirmDialog(true);
    } else {
      await executeAction(action);
    }
  };

  const executeAction = async (action: 'delete' | 'restore') => {
    try {
      if (action === 'delete') {
        await softDelete(item.id);
      } else {
        await restore(item.id);
      }
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const confirmAction = async () => {
    if (pendingAction) {
      await executeAction(pendingAction);
    }
  };

  const cancelAction = () => {
    setShowConfirmDialog(false);
    setPendingAction(null);
  };

  // Size classes
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const iconSizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  // Base button classes
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  // Variant-specific classes
  const getVariantClasses = () => {
    if (isDeleted) {
      // Restore button styling
      switch (variant) {
        case 'icon':
          return 'text-green-600 hover:text-green-700 hover:bg-green-50 p-1 rounded';
        case 'text':
          return 'text-green-600 hover:text-green-700 underline';
        default:
          return 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500';
      }
    } else {
      // Delete button styling
      switch (variant) {
        case 'icon':
          return 'text-red-600 hover:text-red-700 hover:bg-red-50 p-1 rounded';
        case 'text':
          return 'text-red-600 hover:text-red-700 underline';
        default:
          return 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500';
      }
    }
  };

  const buttonClasses = `${baseClasses} ${getVariantClasses()} ${variant !== 'text' ? sizeClasses[size] : ''} ${className}`;

  const renderButton = () => {
    const Icon = isDeleted ? RotateCcw : Trash2;
    const action = isDeleted ? 'restore' : 'delete';
    const label = isDeleted ? 'Restore' : 'Delete';

    return (
      <button
        onClick={() => handleAction(action)}
        disabled={isLoading}
        className={buttonClasses}
        title={`${label} ${itemType}`}
      >
        {isLoading ? (
          <div className={`animate-spin rounded-full border-2 border-current border-t-transparent ${iconSizeClasses[size]}`} />
        ) : (
          <>
            <Icon className={`${iconSizeClasses[size]} ${variant === 'button' ? 'mr-2' : ''}`} />
            {variant === 'button' && <span>{label}</span>}
          </>
        )}
      </button>
    );
  };

  const renderConfirmDialog = () => {
    if (!showConfirmDialog || !pendingAction) return null;

    const message = pendingAction === 'delete'
      ? getSoftDeleteConfirmationMessage(itemType, itemName)
      : getRestoreConfirmationMessage(itemType, itemName);

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
          <div className="flex items-center mb-4">
            <AlertTriangle className="w-6 h-6 text-yellow-600 mr-3" />
            <h3 className="text-lg font-semibold text-gray-900">
              Confirm {pendingAction === 'delete' ? 'Delete' : 'Restore'}
            </h3>
          </div>
          
          <p className="text-gray-600 mb-6">{message}</p>
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={cancelAction}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={confirmAction}
              disabled={isLoading}
              className={`px-4 py-2 text-sm font-medium text-white rounded-md transition-colors ${
                pendingAction === 'delete'
                  ? 'bg-red-600 hover:bg-red-700'
                  : 'bg-green-600 hover:bg-green-700'
              } disabled:opacity-50`}
            >
              {isLoading ? 'Processing...' : `Confirm ${pendingAction === 'delete' ? 'Delete' : 'Restore'}`}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {renderButton()}
      {renderConfirmDialog()}
    </>
  );
}

// Status indicator component
interface SoftDeleteStatusProps {
  item: SoftDeletable;
  showDate?: boolean;
  className?: string;
}

export function SoftDeleteStatus({ 
  item, 
  showDate = false, 
  className = '' 
}: SoftDeleteStatusProps) {
  const isDeleted = isSoftDeleted(item);
  
  if (!isDeleted) {
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 ${className}`}>
        Active
      </span>
    );
  }

  return (
    <div className={`inline-flex items-center ${className}`}>
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
        Deleted
      </span>
      {showDate && item.deletedAt && (
        <span className="ml-2 text-xs text-gray-500">
          {new Date(item.deletedAt).toLocaleDateString()}
        </span>
      )}
    </div>
  );
}

// Bulk operations component
interface BulkSoftDeleteProps {
  selectedIds: string[];
  baseUrl: string;
  itemType: string;
  onSuccess?: (action: 'deleted' | 'restored', count: number) => void;
  onError?: (error: string) => void;
  className?: string;
}

export function BulkSoftDeleteActions({
  selectedIds,
  baseUrl,
  itemType,
  onSuccess,
  onError,
  className = '',
}: BulkSoftDeleteProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<'delete' | 'restore' | null>(null);

  const { isLoading, progress, bulkSoftDelete, bulkRestore } = useBulkSoftDelete(baseUrl, {
    onSuccess: (action, count) => {
      setShowConfirmDialog(false);
      setPendingAction(null);
      onSuccess?.(action, count);
    },
    onError: (error) => {
      setShowConfirmDialog(false);
      setPendingAction(null);
      onError?.(error);
    },
  });

  const handleBulkAction = (action: 'delete' | 'restore') => {
    setPendingAction(action);
    setShowConfirmDialog(true);
  };

  const confirmBulkAction = async () => {
    if (pendingAction === 'delete') {
      await bulkSoftDelete(selectedIds);
    } else if (pendingAction === 'restore') {
      await bulkRestore(selectedIds);
    }
  };

  const cancelBulkAction = () => {
    setShowConfirmDialog(false);
    setPendingAction(null);
  };

  if (selectedIds.length === 0) {
    return null;
  }

  return (
    <>
      <div className={`flex items-center space-x-2 ${className}`}>
        <span className="text-sm text-gray-600">
          {selectedIds.length} selected
        </span>
        
        <button
          onClick={() => handleBulkAction('delete')}
          disabled={isLoading}
          className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
        >
          Delete Selected
        </button>
        
        <button
          onClick={() => handleBulkAction('restore')}
          disabled={isLoading}
          className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          Restore Selected
        </button>
        
        {isLoading && (
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
            <span className="text-sm text-gray-600">{progress}%</span>
          </div>
        )}
      </div>

      {showConfirmDialog && pendingAction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <AlertTriangle className="w-6 h-6 text-yellow-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">
                Confirm Bulk {pendingAction === 'delete' ? 'Delete' : 'Restore'}
              </h3>
            </div>
            
            <p className="text-gray-600 mb-6">
              Are you sure you want to {pendingAction} {selectedIds.length} {itemType}(s)?
              {pendingAction === 'delete' && ' This action can be undone.'}
            </p>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelBulkAction}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
              >
                Cancel
              </button>
              <button
                onClick={confirmBulkAction}
                disabled={isLoading}
                className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                  pendingAction === 'delete'
                    ? 'bg-red-600 hover:bg-red-700'
                    : 'bg-green-600 hover:bg-green-700'
                } disabled:opacity-50`}
              >
                {isLoading ? 'Processing...' : `Confirm ${pendingAction === 'delete' ? 'Delete' : 'Restore'}`}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
