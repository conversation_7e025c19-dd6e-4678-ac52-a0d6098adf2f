const { exec } = require('child_process');
const { promisify } = require('util');
const fs = require('fs');
const path = require('path');

const execAsync = promisify(exec);

async function runDatabaseMigration() {
  console.log('🚀 Starting Complete Database Migration Process');
  console.log('📋 This will:');
  console.log('   1. Generate Prisma client with new schema');
  console.log('   2. Create database migration');
  console.log('   3. Apply schema changes');
  console.log('   4. Run custom enhancement migration');
  console.log('   5. Verify all changes');
  
  try {
    // Step 1: Generate Prisma client
    console.log('\n🔧 Step 1: Generating Prisma client...');
    await execAsync('npx prisma generate');
    console.log('✅ Prisma client generated');
    
    // Step 2: Create migration
    console.log('\n🔧 Step 2: Creating database migration...');
    const migrationName = `database_enhancement_${Date.now()}`;
    await execAsync(`npx prisma migrate dev --name ${migrationName}`);
    console.log('✅ Database migration created and applied');
    
    // Step 3: Run custom enhancement migration
    console.log('\n🔧 Step 3: Running custom enhancement migration...');
    const { enhanceDatabaseSchema } = require('./database-enhancement-migration.js');
    await enhanceDatabaseSchema();
    console.log('✅ Custom enhancement migration completed');
    
    // Step 4: Verify database status
    console.log('\n🔧 Step 4: Verifying database status...');
    await execAsync('npx prisma db push --accept-data-loss');
    console.log('✅ Database schema synchronized');
    
    // Step 5: Generate final client
    console.log('\n🔧 Step 5: Generating final Prisma client...');
    await execAsync('npx prisma generate');
    console.log('✅ Final Prisma client generated');
    
    console.log('\n🎉 Complete Database Migration Process Finished!');
    console.log('\n📊 Summary of Changes:');
    console.log('   ✓ Enhanced schema with performance indexes');
    console.log('   ✓ Added soft delete functionality');
    console.log('   ✓ Implemented audit trail system');
    console.log('   ✓ Created AI caching layer');
    console.log('   ✓ Added normalized data storage');
    console.log('   ✓ Implemented analytics and versioning');
    console.log('   ✓ Added advanced features');
    
    console.log('\n🔍 Next Steps:');
    console.log('   1. Update database service layer to use new features');
    console.log('   2. Implement soft delete in application logic');
    console.log('   3. Add audit trail logging');
    console.log('   4. Implement AI caching');
    console.log('   5. Test all new functionality');
    
  } catch (error) {
    console.error('❌ Migration process failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Check if Prisma is installed: npm install prisma @prisma/client');
    console.log('   2. Verify database connection in .env file');
    console.log('   3. Check backup files in scripts/ directory');
    console.log('   4. Run: npx prisma db push --force-reset (⚠️  This will reset data)');
    throw error;
  }
}

// Helper function to check prerequisites
async function checkPrerequisites() {
  console.log('🔍 Checking prerequisites...');
  
  // Check if Prisma is installed
  try {
    await execAsync('npx prisma --version');
    console.log('✅ Prisma CLI available');
  } catch (error) {
    console.error('❌ Prisma CLI not found. Please install: npm install prisma @prisma/client');
    return false;
  }
  
  // Check if schema file exists
  if (!fs.existsSync(path.join(__dirname, '../prisma/schema.prisma'))) {
    console.error('❌ Prisma schema file not found');
    return false;
  }
  console.log('✅ Prisma schema file found');
  
  // Check if .env file exists
  if (!fs.existsSync(path.join(__dirname, '../.env'))) {
    console.error('❌ .env file not found. Please create it with DATABASE_URL');
    return false;
  }
  console.log('✅ Environment file found');
  
  return true;
}

// Main execution
async function main() {
  try {
    const prerequisitesOk = await checkPrerequisites();
    if (!prerequisitesOk) {
      console.error('❌ Prerequisites check failed. Please fix the issues above.');
      process.exit(1);
    }
    
    await runDatabaseMigration();
    console.log('\n🎉 All done! Database enhancement completed successfully.');
    
  } catch (error) {
    console.error('\n💥 Migration failed:', error.message);
    console.log('\n📞 If you need help:');
    console.log('   1. Check the backup files in scripts/ directory');
    console.log('   2. Review the error message above');
    console.log('   3. Consider running individual migration steps manually');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { runDatabaseMigration, checkPrerequisites };
