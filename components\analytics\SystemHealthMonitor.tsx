'use client';

import React, { useState, useEffect } from 'react';
import { 
  Activity, Database, Zap, AlertTriangle, CheckCircle, 
  XCircle, Clock, RefreshCw, Server, Wifi 
} from 'lucide-react';

interface SystemHealth {
  overall: 'healthy' | 'warning' | 'critical';
  services: {
    database: {
      status: 'online' | 'offline' | 'degraded';
      responseTime: number;
      connections: number;
      lastCheck: string;
    };
    aiService: {
      status: 'online' | 'offline' | 'degraded';
      responseTime: number;
      requestsPerMinute: number;
      errorRate: number;
      lastCheck: string;
    };
    cache: {
      status: 'online' | 'offline' | 'degraded';
      hitRate: number;
      memoryUsage: number;
      entries: number;
      lastCheck: string;
    };
    api: {
      status: 'online' | 'offline' | 'degraded';
      responseTime: number;
      requestsPerMinute: number;
      errorRate: number;
      lastCheck: string;
    };
  };
  alerts: {
    id: string;
    type: 'error' | 'warning' | 'info';
    message: string;
    timestamp: string;
    resolved: boolean;
  }[];
  uptime: {
    current: number; // in hours
    last24h: number; // percentage
    last7d: number; // percentage
    last30d: number; // percentage
  };
}

interface SystemHealthMonitorProps {
  className?: string;
}

export function SystemHealthMonitor({ className }: SystemHealthMonitorProps) {
  const [health, setHealth] = useState<SystemHealth | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchSystemHealth = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/system/health');
      if (!response.ok) {
        throw new Error('Failed to fetch system health');
      }

      const result = await response.json();
      setHealth(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch system health');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemHealth();
    
    if (autoRefresh) {
      const interval = setInterval(fetchSystemHealth, 30000); // 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'offline':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'offline':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getOverallStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'critical':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatUptime = (hours: number) => {
    if (hours < 24) {
      return `${hours.toFixed(1)}h`;
    }
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}d ${remainingHours.toFixed(0)}h`;
  };

  const formatLastCheck = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    
    if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else {
      return date.toLocaleTimeString('id-ID');
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            System Health Monitor
          </h3>
          <div className="flex items-center space-x-4">
            <label className="flex items-center text-sm text-gray-600">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="mr-2"
              />
              Auto-refresh
            </label>
            <button
              onClick={fetchSystemHealth}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh health status"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <div className="flex">
            <AlertTriangle className="w-5 h-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="p-6">
        {health ? (
          <div className="space-y-6">
            {/* Overall Status */}
            <div className={`p-4 rounded-lg border ${getOverallStatusColor(health.overall)}`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Activity className="w-6 h-6 mr-3" />
                  <div>
                    <h4 className="font-semibold">Overall System Status</h4>
                    <p className="text-sm capitalize">{health.overall}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">Current Uptime</p>
                  <p className="text-lg font-bold">{formatUptime(health.uptime.current)}</p>
                </div>
              </div>
            </div>

            {/* Service Status Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Database */}
              <div className={`p-4 rounded-lg border ${getStatusColor(health.services.database.status)}`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <Database className="w-5 h-5 mr-2" />
                    <span className="font-medium">Database</span>
                  </div>
                  {getStatusIcon(health.services.database.status)}
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Response Time:</span>
                    <span>{health.services.database.responseTime}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Connections:</span>
                    <span>{health.services.database.connections}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Check:</span>
                    <span>{formatLastCheck(health.services.database.lastCheck)}</span>
                  </div>
                </div>
              </div>

              {/* AI Service */}
              <div className={`p-4 rounded-lg border ${getStatusColor(health.services.aiService.status)}`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <Zap className="w-5 h-5 mr-2" />
                    <span className="font-medium">AI Service</span>
                  </div>
                  {getStatusIcon(health.services.aiService.status)}
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Response Time:</span>
                    <span>{health.services.aiService.responseTime}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Requests/min:</span>
                    <span>{health.services.aiService.requestsPerMinute}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Error Rate:</span>
                    <span>{(health.services.aiService.errorRate * 100).toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Check:</span>
                    <span>{formatLastCheck(health.services.aiService.lastCheck)}</span>
                  </div>
                </div>
              </div>

              {/* Cache */}
              <div className={`p-4 rounded-lg border ${getStatusColor(health.services.cache.status)}`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <Server className="w-5 h-5 mr-2" />
                    <span className="font-medium">Cache</span>
                  </div>
                  {getStatusIcon(health.services.cache.status)}
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Hit Rate:</span>
                    <span>{(health.services.cache.hitRate * 100).toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory Usage:</span>
                    <span>{health.services.cache.memoryUsage.toFixed(1)}MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Entries:</span>
                    <span>{health.services.cache.entries}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Check:</span>
                    <span>{formatLastCheck(health.services.cache.lastCheck)}</span>
                  </div>
                </div>
              </div>

              {/* API */}
              <div className={`p-4 rounded-lg border ${getStatusColor(health.services.api.status)}`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <Wifi className="w-5 h-5 mr-2" />
                    <span className="font-medium">API</span>
                  </div>
                  {getStatusIcon(health.services.api.status)}
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Response Time:</span>
                    <span>{health.services.api.responseTime}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Requests/min:</span>
                    <span>{health.services.api.requestsPerMinute}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Error Rate:</span>
                    <span>{(health.services.api.errorRate * 100).toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Check:</span>
                    <span>{formatLastCheck(health.services.api.lastCheck)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Uptime Statistics */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-3">Uptime Statistics</h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-green-600">{health.uptime.last24h.toFixed(1)}%</p>
                  <p className="text-sm text-gray-600">Last 24 Hours</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-600">{health.uptime.last7d.toFixed(1)}%</p>
                  <p className="text-sm text-gray-600">Last 7 Days</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-600">{health.uptime.last30d.toFixed(1)}%</p>
                  <p className="text-sm text-gray-600">Last 30 Days</p>
                </div>
              </div>
            </div>

            {/* Recent Alerts */}
            {health.alerts.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Recent Alerts</h4>
                <div className="space-y-2">
                  {health.alerts.slice(0, 5).map((alert) => (
                    <div 
                      key={alert.id}
                      className={`p-3 rounded-lg border-l-4 ${
                        alert.type === 'error' ? 'bg-red-50 border-red-400' :
                        alert.type === 'warning' ? 'bg-yellow-50 border-yellow-400' :
                        'bg-blue-50 border-blue-400'
                      } ${alert.resolved ? 'opacity-60' : ''}`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {alert.type === 'error' && <XCircle className="w-4 h-4 text-red-500 mr-2" />}
                          {alert.type === 'warning' && <AlertTriangle className="w-4 h-4 text-yellow-500 mr-2" />}
                          {alert.type === 'info' && <CheckCircle className="w-4 h-4 text-blue-500 mr-2" />}
                          <span className="text-sm font-medium">{alert.message}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {alert.resolved && (
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                              Resolved
                            </span>
                          )}
                          <span className="text-xs text-gray-500">
                            {formatLastCheck(alert.timestamp)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          !isLoading && (
            <div className="text-center py-8">
              <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No system health data available.</p>
            </div>
          )
        )}

        {isLoading && (
          <div className="text-center py-8">
            <RefreshCw className="w-8 h-8 text-gray-400 mx-auto mb-4 animate-spin" />
            <p className="text-gray-500">Loading system health status...</p>
          </div>
        )}
      </div>
    </div>
  );
}
