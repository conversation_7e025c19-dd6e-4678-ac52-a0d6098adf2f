# Database Enhancement Deployment Guide

## 🚀 Quick Start

### One-Command Deployment
```bash
node scripts/deploy-database-enhancements.js
```

This single command will:
- ✅ Run all pre-deployment checks
- ✅ Create automatic backups
- ✅ Apply schema migrations
- ✅ Add performance indexes
- ✅ Implement soft delete
- ✅ Add data validation
- ✅ Create audit trail system
- ✅ Set up AI caching
- ✅ Add analytics tables
- ✅ Run comprehensive tests
- ✅ Generate deployment report

## 📋 Prerequisites

### Required Software
- Node.js 16+ 
- npm or yarn
- SQLite (for local development)

### Required Packages
```bash
npm install prisma @prisma/client
```

### Environment Setup
Ensure your `.env` file contains:
```env
DATABASE_URL="file:./dev.db"
```

## 🔧 Manual Deployment Steps

If you prefer to run individual steps:

### 1. Schema Migration
```bash
npx prisma generate
npx prisma migrate dev --name database_enhancements
```

### 2. Custom Enhancements
```bash
node scripts/database-enhancement-migration.js
```

### 3. Data Validation
```bash
node scripts/add-data-validation.js
```

### 4. Testing
```bash
node scripts/test-database-enhancements.js
```

## 📊 What Gets Enhanced

### Performance Improvements
- **Strategic Indexes**: 15+ indexes for common query patterns
- **Query Optimization**: Composite indexes for complex queries
- **Soft Delete Filtering**: Efficient exclusion of deleted records

### Data Integrity
- **RIASEC Validation**: Scores must be 0-30
- **OCEAN Validation**: Scores must be 5-25
- **Email Validation**: Proper format checking
- **Referential Integrity**: Foreign key constraints

### New Features
- **Audit Trail**: Complete change tracking
- **AI Caching**: Intelligent result caching with hit counting
- **Normalized Storage**: Separate tables for JSON arrays
- **Analytics**: Daily aggregation and reporting
- **Versioning**: Profile change history
- **Advanced Features**: Comparisons and recommendations

### Database Tables Added
1. `assessment_history` - Audit trail
2. `ai_cache` - AI result caching
3. `profile_strengths` - Normalized strengths
4. `career_suggestions` - Normalized career data
5. `development_areas` - Normalized development data
6. `assessment_analytics` - Daily analytics
7. `user_sessions` - Session tracking
8. `profile_versions` - Version control
9. `profile_comparisons` - Comparison feature
10. `career_recommendations` - Enhanced recommendations

## 🔍 Verification

### Check Deployment Status
```bash
# View database schema
npx prisma studio

# Check table structure
node -e "
const { PrismaClient } = require('./lib/generated/prisma');
const prisma = new PrismaClient();
prisma.\$queryRaw\`SELECT name FROM sqlite_master WHERE type='table'\`
  .then(tables => console.log('Tables:', tables))
  .finally(() => prisma.\$disconnect());
"
```

### Run Tests
```bash
node scripts/test-database-enhancements.js
```

### Check Performance
```bash
# View indexes
node -e "
const { PrismaClient } = require('./lib/generated/prisma');
const prisma = new PrismaClient();
prisma.\$queryRaw\`SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'\`
  .then(indexes => console.log('Indexes:', indexes))
  .finally(() => prisma.\$disconnect());
"
```

## 🔄 Using Enhanced Features

### Enhanced Database Service
```typescript
import { enhancedDb } from '@/lib/server/enhanced-database';

// Create assessment with audit trail
const assessment = await enhancedDb.createAssessment(
  riasecScores,
  oceanScores,
  userId,
  { ipAddress: req.ip, userAgent: req.headers['user-agent'] }
);

// Check AI cache
const cached = await enhancedDb.checkAiCache(riasecScores, oceanScores);
if (cached) {
  return cached; // Return cached result
}

// Generate and cache new result
const result = await generateProfile(riasecScores, oceanScores);
await enhancedDb.storeAiCache(riasecScores, oceanScores, result);
```

### Soft Delete Queries
```typescript
// Get active assessments only (default behavior)
const activeAssessments = await prisma.assessment.findMany({
  where: { deletedAt: null }
});

// Include deleted assessments
const allAssessments = await enhancedDb.getAssessment(id, true);

// Soft delete an assessment
await enhancedDb.softDeleteAssessment(assessmentId, userId);
```

### Analytics Usage
```typescript
// Update daily analytics
await enhancedDb.updateDailyAnalytics(new Date());

// Get analytics data
const analytics = await prisma.assessmentAnalytics.findMany({
  where: {
    date: {
      gte: new Date('2024-01-01'),
      lte: new Date('2024-12-31')
    }
  },
  orderBy: { date: 'desc' }
});
```

## 🛠️ Troubleshooting

### Common Issues

#### Migration Fails
```bash
# Reset and retry
npx prisma migrate reset --force
node scripts/deploy-database-enhancements.js
```

#### Validation Errors
```bash
# Check constraint violations
node scripts/test-database-enhancements.js
```

#### Performance Issues
```bash
# Analyze query performance
npx prisma studio
# Check EXPLAIN QUERY PLAN in SQLite
```

### Recovery Options

#### Restore from Backup
```bash
# Backups are automatically created in scripts/ directory
# Look for files like: backup-before-enhancement.json
```

#### Rollback Migration
```bash
npx prisma migrate reset --force
# Then restore from backup manually
```

## 📈 Monitoring

### Performance Metrics
- Query execution times
- Cache hit rates
- Index usage statistics
- Database size growth

### Data Quality
- Constraint violation counts
- Orphaned record detection
- Data consistency checks

### Maintenance Tasks
- Daily analytics aggregation
- Weekly cache cleanup
- Monthly audit trail archival

## 🔮 Next Steps

After successful deployment:

1. **Update Application Code**
   - Replace old database service with `EnhancedDatabaseService`
   - Implement soft delete in UI components
   - Add audit trail logging to critical operations

2. **Configure Monitoring**
   - Set up analytics dashboard
   - Configure cache cleanup schedules
   - Implement performance monitoring

3. **User Training**
   - Document new features for team
   - Update API documentation
   - Create user guides for new functionality

4. **Production Deployment**
   - Test in staging environment
   - Plan production deployment window
   - Prepare rollback procedures

## 📞 Support

### Getting Help
- Check backup files in `scripts/` directory
- Review error logs in console output
- Consult `docs/DATABASE_ENHANCEMENTS.md`
- Run individual test components for debugging

### Useful Commands
```bash
# Check database status
npx prisma db status

# View database in browser
npx prisma studio

# Generate fresh client
npx prisma generate

# Reset everything (⚠️ DESTRUCTIVE)
npx prisma migrate reset --force
```

---

**Last Updated**: 2024-01-15  
**Version**: 1.0.0  
**Deployment Time**: ~5-10 minutes  
**Rollback Time**: ~2-3 minutes
