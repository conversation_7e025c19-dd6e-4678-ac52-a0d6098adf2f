import { NextRequest, NextResponse } from 'next/server';
import { enhancedDb } from '@/lib/server/enhanced-database';

interface SystemHealth {
  overall: 'healthy' | 'warning' | 'critical';
  services: {
    database: {
      status: 'online' | 'offline' | 'degraded';
      responseTime: number;
      connections: number;
      lastCheck: string;
    };
    aiService: {
      status: 'online' | 'offline' | 'degraded';
      responseTime: number;
      requestsPerMinute: number;
      errorRate: number;
      lastCheck: string;
    };
    cache: {
      status: 'online' | 'offline' | 'degraded';
      hitRate: number;
      memoryUsage: number;
      entries: number;
      lastCheck: string;
    };
    api: {
      status: 'online' | 'offline' | 'degraded';
      responseTime: number;
      requestsPerMinute: number;
      errorRate: number;
      lastCheck: string;
    };
  };
  alerts: {
    id: string;
    type: 'error' | 'warning' | 'info';
    message: string;
    timestamp: string;
    resolved: boolean;
  }[];
  uptime: {
    current: number; // in hours
    last24h: number; // percentage
    last7d: number; // percentage
    last30d: number; // percentage
  };
}

export async function GET(request: NextRequest) {
  try {
    const now = new Date();
    
    // Check database health
    const dbHealth = await checkDatabaseHealth();
    
    // Check AI service health
    const aiHealth = await checkAIServiceHealth();
    
    // Check cache health
    const cacheHealth = await checkCacheHealth();
    
    // Check API health (self-check)
    const apiHealth = await checkAPIHealth();
    
    // Determine overall health
    const services = [dbHealth, aiHealth, cacheHealth, apiHealth];
    const offlineServices = services.filter(s => s.status === 'offline').length;
    const degradedServices = services.filter(s => s.status === 'degraded').length;
    
    let overall: 'healthy' | 'warning' | 'critical';
    if (offlineServices > 0) {
      overall = 'critical';
    } else if (degradedServices > 1) {
      overall = 'warning';
    } else {
      overall = 'healthy';
    }
    
    // Generate mock alerts for demonstration
    const alerts = generateMockAlerts();
    
    // Calculate uptime (mock data for demonstration)
    const uptime = {
      current: 72.5, // 3 days, 30 minutes
      last24h: 99.8,
      last7d: 99.5,
      last30d: 99.2,
    };
    
    const healthData: SystemHealth = {
      overall,
      services: {
        database: dbHealth,
        aiService: aiHealth,
        cache: cacheHealth,
        api: apiHealth,
      },
      alerts,
      uptime,
    };

    return NextResponse.json({
      success: true,
      data: healthData,
      timestamp: now.toISOString(),
    });

  } catch (error) {
    console.error('System health check error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check system health',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

async function checkDatabaseHealth() {
  const startTime = Date.now();
  
  try {
    // Test database connection with a simple query
    await enhancedDb.getAnalyticsOverview();
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'online' as const,
      responseTime,
      connections: 5, // Mock connection count
      lastCheck: new Date().toISOString(),
    };
  } catch (error) {
    return {
      status: 'offline' as const,
      responseTime: Date.now() - startTime,
      connections: 0,
      lastCheck: new Date().toISOString(),
    };
  }
}

async function checkAIServiceHealth() {
  const startTime = Date.now();
  
  try {
    // Check Gemini service health
    const { EnhancedGeminiProfileService } = await import('@/lib/server/enhanced-gemini-service');
    const geminiService = EnhancedGeminiProfileService.getInstance();
    const metrics = geminiService.getMetrics();
    
    const responseTime = Date.now() - startTime;
    const errorRate = metrics.totalRequests > 0 ? metrics.errorCount / metrics.totalRequests : 0;
    
    let status: 'online' | 'offline' | 'degraded';
    if (responseTime > 5000 || errorRate > 0.1) {
      status = 'degraded';
    } else {
      status = 'online';
    }
    
    return {
      status,
      responseTime: metrics.averageResponseTime || responseTime,
      requestsPerMinute: Math.floor(metrics.totalRequests / 60), // Rough estimate
      errorRate,
      lastCheck: new Date().toISOString(),
    };
  } catch (error) {
    return {
      status: 'offline' as const,
      responseTime: Date.now() - startTime,
      requestsPerMinute: 0,
      errorRate: 1,
      lastCheck: new Date().toISOString(),
    };
  }
}

async function checkCacheHealth() {
  const startTime = Date.now();
  
  try {
    // Check cache statistics
    const cacheStats = await enhancedDb.getCacheStats();
    const responseTime = Date.now() - startTime;
    
    let status: 'online' | 'offline' | 'degraded';
    if (cacheStats.hitRate < 0.5) {
      status = 'degraded';
    } else {
      status = 'online';
    }
    
    return {
      status,
      hitRate: cacheStats.hitRate,
      memoryUsage: cacheStats.totalEntries * 0.5, // Mock memory usage (KB)
      entries: cacheStats.totalEntries,
      lastCheck: new Date().toISOString(),
    };
  } catch (error) {
    return {
      status: 'offline' as const,
      hitRate: 0,
      memoryUsage: 0,
      entries: 0,
      lastCheck: new Date().toISOString(),
    };
  }
}

async function checkAPIHealth() {
  // Self-check - if we're running this code, the API is online
  return {
    status: 'online' as const,
    responseTime: 50, // Mock response time
    requestsPerMinute: 25, // Mock request rate
    errorRate: 0.001, // Mock error rate
    lastCheck: new Date().toISOString(),
  };
}

function generateMockAlerts() {
  const alerts = [];
  const now = new Date();
  
  // Add some sample alerts
  if (Math.random() > 0.7) {
    alerts.push({
      id: 'alert-1',
      type: 'warning' as const,
      message: 'Cache hit rate below optimal threshold (75%)',
      timestamp: new Date(now.getTime() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
      resolved: false,
    });
  }
  
  if (Math.random() > 0.8) {
    alerts.push({
      id: 'alert-2',
      type: 'info' as const,
      message: 'Scheduled maintenance completed successfully',
      timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      resolved: true,
    });
  }
  
  if (Math.random() > 0.9) {
    alerts.push({
      id: 'alert-3',
      type: 'error' as const,
      message: 'High response time detected on AI service',
      timestamp: new Date(now.getTime() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
      resolved: false,
    });
  }
  
  return alerts;
}
