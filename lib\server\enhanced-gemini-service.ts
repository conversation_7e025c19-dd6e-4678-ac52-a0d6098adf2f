import { GoogleGenAI, Type } from '@google/genai';
import { RiasecScores, OceanScores } from '@/lib/types';
import { CombinedProfileInterpretation } from '@/lib/profileStore';
import { enhancedDb } from './enhanced-database';

// Rate limiting configuration
interface RateLimitConfig {
  maxRequestsPerMinute: number;
  maxRequestsPerHour: number;
  maxRequestsPerDay: number;
  retryAttempts: number;
  retryDelay: number;
  backoffMultiplier: number;
}

// Cache configuration
interface CacheConfig {
  enabled: boolean;
  defaultTTL: number; // Time to live in hours
  maxCacheSize: number;
  cleanupInterval: number; // Cleanup interval in hours
}

// Performance metrics
interface PerformanceMetrics {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageResponseTime: number;
  errorCount: number;
  lastCleanup: Date;
}

export class EnhancedGeminiProfileService {
  private static instance: EnhancedGeminiProfileService;
  private genAI: GoogleGenAI;
  private requestQueue: Array<() => Promise<any>> = [];
  private isProcessingQueue = false;
  private rateLimitConfig: RateLimitConfig;
  private cacheConfig: CacheConfig;
  private metrics: PerformanceMetrics;
  private requestHistory: Date[] = [];

  private constructor() {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is required');
    }

    this.genAI = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });

    // Initialize configurations
    this.rateLimitConfig = {
      maxRequestsPerMinute: 15,
      maxRequestsPerHour: 1000,
      maxRequestsPerDay: 50000,
      retryAttempts: 3,
      retryDelay: 1000,
      backoffMultiplier: 2,
    };

    this.cacheConfig = {
      enabled: true,
      defaultTTL: 24 * 7, // 1 week
      maxCacheSize: 10000,
      cleanupInterval: 24, // Daily cleanup
    };

    this.metrics = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      errorCount: 0,
      lastCleanup: new Date(),
    };

    // Start periodic cleanup
    this.startPeriodicCleanup();
  }

  static getInstance(): EnhancedGeminiProfileService {
    if (!EnhancedGeminiProfileService.instance) {
      EnhancedGeminiProfileService.instance = new EnhancedGeminiProfileService();
    }
    return EnhancedGeminiProfileService.instance;
  }

  // Generate combined profile with caching and rate limiting
  async generateCombinedProfile(
    riasecScores: RiasecScores,
    oceanScores: OceanScores,
    options: {
      bypassCache?: boolean;
      priority?: 'low' | 'normal' | 'high';
      cacheTTL?: number;
    } = {}
  ): Promise<CombinedProfileInterpretation> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Check cache first (unless bypassed)
      if (this.cacheConfig.enabled && !options.bypassCache) {
        const cachedResult = await enhancedDb.checkAiCache(riasecScores, oceanScores);
        if (cachedResult) {
          this.metrics.cacheHits++;
          console.log('✅ Cache hit - returning cached profile');
          return cachedResult;
        }
      }

      this.metrics.cacheMisses++;
      console.log('🔄 Cache miss - generating new profile');

      // Generate new profile with rate limiting
      const profile = await this.generateWithRateLimit(riasecScores, oceanScores, options.priority);

      // Store in cache
      if (this.cacheConfig.enabled) {
        const ttl = options.cacheTTL || this.cacheConfig.defaultTTL;
        await enhancedDb.storeAiCache(riasecScores, oceanScores, profile, ttl);
        console.log(`💾 Profile cached with TTL: ${ttl} hours`);
      }

      // Update metrics
      const responseTime = Date.now() - startTime;
      this.updateAverageResponseTime(responseTime);

      return profile;

    } catch (error) {
      this.metrics.errorCount++;
      console.error('❌ Enhanced Gemini service error:', error);
      throw error;
    }
  }

  // Generate profile with rate limiting
  private async generateWithRateLimit(
    riasecScores: RiasecScores,
    oceanScores: OceanScores,
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): Promise<CombinedProfileInterpretation> {
    return new Promise((resolve, reject) => {
      const task = async () => {
        try {
          // Check rate limits
          await this.checkRateLimits();
          
          // Generate profile
          const profile = await this.generateProfileInternal(riasecScores, oceanScores);
          
          // Record request
          this.requestHistory.push(new Date());
          
          resolve(profile);
        } catch (error) {
          reject(error);
        }
      };

      // Add to queue based on priority
      if (priority === 'high') {
        this.requestQueue.unshift(task);
      } else {
        this.requestQueue.push(task);
      }

      this.processQueue();
    });
  }

  // Process request queue
  private async processQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const task = this.requestQueue.shift();
      if (task) {
        try {
          await task();
        } catch (error) {
          console.error('Queue task error:', error);
        }
        
        // Small delay between requests
        await this.delay(100);
      }
    }

    this.isProcessingQueue = false;
  }

  // Check rate limits
  private async checkRateLimits() {
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Clean old requests
    this.requestHistory = this.requestHistory.filter(date => date > oneDayAgo);

    const requestsLastMinute = this.requestHistory.filter(date => date > oneMinuteAgo).length;
    const requestsLastHour = this.requestHistory.filter(date => date > oneHourAgo).length;
    const requestsLastDay = this.requestHistory.length;

    // Check limits
    if (requestsLastMinute >= this.rateLimitConfig.maxRequestsPerMinute) {
      const waitTime = 60 - (now.getTime() - oneMinuteAgo.getTime()) / 1000;
      console.log(`⏳ Rate limit: waiting ${waitTime}s (minute limit)`);
      await this.delay(waitTime * 1000);
    }

    if (requestsLastHour >= this.rateLimitConfig.maxRequestsPerHour) {
      const waitTime = 3600 - (now.getTime() - oneHourAgo.getTime()) / 1000;
      console.log(`⏳ Rate limit: waiting ${waitTime}s (hour limit)`);
      await this.delay(waitTime * 1000);
    }

    if (requestsLastDay >= this.rateLimitConfig.maxRequestsPerDay) {
      throw new Error('Daily rate limit exceeded');
    }
  }

  // Internal profile generation (same as original)
  private async generateProfileInternal(
    riasecScores: RiasecScores,
    oceanScores: OceanScores
  ): Promise<CombinedProfileInterpretation> {
    const prompt = this.buildPrompt(riasecScores, oceanScores);
    
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.rateLimitConfig.retryAttempts; attempt++) {
      try {
        console.log(`🤖 Generating profile (attempt ${attempt}/${this.rateLimitConfig.retryAttempts})`);

        // Use the same API as geminiService.ts
        const response = await this.genAI.models.generateContent({
          model: "gemini-2.5-pro",
          contents: prompt,
          config: {
            responseMimeType: "application/json",
            temperature: 0.7,
            topP: 0.95,
            topK: 40
          }
        });

        const text = response.text;
        if (!text) {
          throw new Error('Empty response from Gemini AI');
        }

        const profile = JSON.parse(text);

        // Validate the response structure
        this.validateProfileStructure(profile);

        return profile;
        
      } catch (error) {
        lastError = error as Error;
        console.error(`❌ Attempt ${attempt} failed:`, error);
        
        if (attempt < this.rateLimitConfig.retryAttempts) {
          const delay = this.rateLimitConfig.retryDelay * Math.pow(this.rateLimitConfig.backoffMultiplier, attempt - 1);
          console.log(`⏳ Retrying in ${delay}ms...`);
          await this.delay(delay);
        }
      }
    }
    
    throw lastError || new Error('Profile generation failed after all retry attempts');
  }

  // Build prompt (same as original)
  private buildPrompt(riasecScores: RiasecScores, oceanScores: OceanScores): string {
    return `Analisis profil kepribadian dan minat karir berdasarkan skor RIASEC dan Big Five (OCEAN).

SKOR RIASEC:
- Realistic (R): ${riasecScores.R}/30
- Investigative (I): ${riasecScores.I}/30  
- Artistic (A): ${riasecScores.A}/30
- Social (S): ${riasecScores.S}/30
- Enterprising (E): ${riasecScores.E}/30
- Conventional (C): ${riasecScores.C}/30

SKOR BIG FIVE (OCEAN):
- Openness (O): ${oceanScores.O}/25
- Conscientiousness (C): ${oceanScores.C}/25
- Extraversion (E): ${oceanScores.E}/25
- Agreeableness (A): ${oceanScores.A}/25
- Neuroticism (N): ${oceanScores.N}/25

Berikan analisis dalam format JSON dengan struktur berikut:
{
  "profileTitle": "Judul profil singkat dan menarik",
  "profileDescription": "Deskripsi komprehensif kepribadian (2-3 paragraf)",
  "strengths": ["kekuatan 1", "kekuatan 2", "kekuatan 3", "kekuatan 4", "kekuatan 5"],
  "careerSuggestions": ["karir 1", "karir 2", "karir 3", "karir 4", "karir 5", "karir 6", "karir 7", "karir 8"],
  "workEnvironment": "Deskripsi lingkungan kerja yang ideal (1 paragraf)",
  "developmentAreas": ["area pengembangan 1", "area pengembangan 2", "area pengembangan 3", "area pengembangan 4"],
  "personalityInsights": ["insight 1", "insight 2", "insight 3", "insight 4", "insight 5"],
  "careerFit": "Penjelasan kesesuaian karir berdasarkan kombinasi RIASEC dan Big Five (1 paragraf)"
}

Gunakan bahasa Indonesia yang natural dan profesional. Fokus pada aspek positif sambil memberikan saran pengembangan yang konstruktif.`;
  }

  // Validate profile structure
  private validateProfileStructure(profile: any): void {
    const requiredFields = [
      'profileTitle', 'profileDescription', 'strengths', 'careerSuggestions',
      'workEnvironment', 'developmentAreas', 'personalityInsights', 'careerFit'
    ];

    for (const field of requiredFields) {
      if (!profile[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate array fields
    const arrayFields = ['strengths', 'careerSuggestions', 'developmentAreas', 'personalityInsights'];
    for (const field of arrayFields) {
      if (!Array.isArray(profile[field]) || profile[field].length === 0) {
        throw new Error(`Field ${field} must be a non-empty array`);
      }
    }
  }

  // Utility methods
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private updateAverageResponseTime(responseTime: number): void {
    const totalTime = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime;
    this.metrics.averageResponseTime = totalTime / this.metrics.totalRequests;
  }

  // Periodic cleanup
  private startPeriodicCleanup(): void {
    setInterval(async () => {
      try {
        const deleted = await enhancedDb.cleanExpiredCache();
        console.log(`🧹 Cache cleanup: removed ${deleted} expired entries`);
        this.metrics.lastCleanup = new Date();
      } catch (error) {
        console.error('Cache cleanup error:', error);
      }
    }, this.cacheConfig.cleanupInterval * 60 * 60 * 1000);
  }

  // Get performance metrics
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // Get cache statistics
  async getCacheStats() {
    const stats = await enhancedDb.getCacheStats();
    return {
      ...stats,
      hitRate: this.metrics.totalRequests > 0 ? (this.metrics.cacheHits / this.metrics.totalRequests) * 100 : 0,
    };
  }

  // Clear cache
  async clearCache(): Promise<number> {
    return await enhancedDb.clearCache();
  }

  // Update configuration
  updateConfig(config: Partial<RateLimitConfig & CacheConfig>): void {
    this.rateLimitConfig = { ...this.rateLimitConfig, ...config };
    this.cacheConfig = { ...this.cacheConfig, ...config };
  }
}
