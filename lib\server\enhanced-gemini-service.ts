import { GoogleGenAI, Type } from '@google/genai';
import { RiasecScores, OceanScores } from '@/lib/types';
import { CombinedProfileInterpretation } from '@/lib/profileStore';
import { enhancedDb } from './enhanced-database';

// Schema untuk response gabungan RIASEC + OCEAN dengan structured output
const combinedProfileResponseSchema = {
  type: Type.OBJECT,
  properties: {
    profileTitle: {
      type: Type.STRING,
      description: "Judul profil yang menggabungkan insights RIASEC dan OCEAN (Dengan Bahasa inggris yang singkat)"
    },
    profileDescription: {
      type: Type.STRING,
      description: "Deskripsi kepribadian dalam DUA kalimat yang mengintegrasikan kedua model 2 kalimat singkat"
    },
    strengths: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 kekuatan utama berdasarkan kombinasi RIASEC dan OCEAN dalam SATU kalimat singkat"
    },
    careerSuggestions: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 rekomendasi karier yang mempertimbangkan minat dan kepribadian (use english)"
    },
    workEnvironment: {
      type: Type.STRING,
      description: "Gaya kerja ideal berdasarkan profil kepribadian dalam 2-3 kalimat singkat"
    },
    developmentAreas: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "3-4 area pengembangan berdasarkan analisis profil dalam SATU kalimat singkat"
    },
    personalityInsights: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "3-4 insight kepribadian unik dari kombinasi RIASEC dan OCEAN dalam SATU kalimat singkat"
    },
    careerFit: {
      type: Type.STRING,
      description: "Penjelasan mengapa karier yang disarankan cocok dengan profil dalam 2-3 kalimat singkat"
    }
  },
  propertyOrdering: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workEnvironment", "developmentAreas", "personalityInsights", "careerFit"],
  required: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workEnvironment", "developmentAreas", "personalityInsights", "careerFit"]
};

// Rate limiting configuration
interface RateLimitConfig {
  maxRequestsPerMinute: number;
  maxRequestsPerHour: number;
  maxRequestsPerDay: number;
  retryAttempts: number;
  retryDelay: number;
  backoffMultiplier: number;
}

// Cache configuration
interface CacheConfig {
  enabled: boolean;
  defaultTTL: number; // Time to live in hours
  maxCacheSize: number;
  cleanupInterval: number; // Cleanup interval in hours
}

// Performance metrics
interface PerformanceMetrics {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageResponseTime: number;
  errorCount: number;
  lastCleanup: Date;
}

export class EnhancedGeminiProfileService {
  private static instance: EnhancedGeminiProfileService;
  private genAI: GoogleGenAI;
  private requestQueue: Array<() => Promise<any>> = [];
  private isProcessingQueue = false;
  private rateLimitConfig: RateLimitConfig;
  private cacheConfig: CacheConfig;
  private metrics: PerformanceMetrics;
  private requestHistory: Date[] = [];

  private constructor() {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is required');
    }

    this.genAI = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });

    // Initialize configurations
    this.rateLimitConfig = {
      maxRequestsPerMinute: 15,
      maxRequestsPerHour: 1000,
      maxRequestsPerDay: 50000,
      retryAttempts: 3,
      retryDelay: 1000,
      backoffMultiplier: 2,
    };

    this.cacheConfig = {
      enabled: true,
      defaultTTL: 24 * 7, // 1 week
      maxCacheSize: 10000,
      cleanupInterval: 24, // Daily cleanup
    };

    this.metrics = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      errorCount: 0,
      lastCleanup: new Date(),
    };

    // Start periodic cleanup
    this.startPeriodicCleanup();
  }

  static getInstance(): EnhancedGeminiProfileService {
    if (!EnhancedGeminiProfileService.instance) {
      EnhancedGeminiProfileService.instance = new EnhancedGeminiProfileService();
    }
    return EnhancedGeminiProfileService.instance;
  }

  // Generate combined profile with caching and rate limiting
  async generateCombinedProfile(
    riasecScores: RiasecScores,
    oceanScores: OceanScores,
    options: {
      bypassCache?: boolean;
      priority?: 'low' | 'normal' | 'high';
      cacheTTL?: number;
    } = {}
  ): Promise<CombinedProfileInterpretation> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Check cache first (unless bypassed)
      if (this.cacheConfig.enabled && !options.bypassCache) {
        const cachedResult = await enhancedDb.checkAiCache(riasecScores, oceanScores);
        if (cachedResult) {
          this.metrics.cacheHits++;
          console.log('✅ Cache hit - returning cached profile');
          return cachedResult;
        }
      }

      this.metrics.cacheMisses++;
      console.log('🔄 Cache miss - generating new profile');

      // Generate new profile with rate limiting
      const profile = await this.generateWithRateLimit(riasecScores, oceanScores, options.priority);

      // Store in cache
      if (this.cacheConfig.enabled) {
        const ttl = options.cacheTTL || this.cacheConfig.defaultTTL;
        await enhancedDb.storeAiCache(riasecScores, oceanScores, profile, ttl);
        console.log(`💾 Profile cached with TTL: ${ttl} hours`);
      }

      // Update metrics
      const responseTime = Date.now() - startTime;
      this.updateAverageResponseTime(responseTime);

      return profile;

    } catch (error) {
      this.metrics.errorCount++;
      console.error('❌ Enhanced Gemini service error:', error);
      throw error;
    }
  }

  // Generate profile with rate limiting
  private async generateWithRateLimit(
    riasecScores: RiasecScores,
    oceanScores: OceanScores,
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): Promise<CombinedProfileInterpretation> {
    return new Promise((resolve, reject) => {
      const task = async () => {
        try {
          // Check rate limits
          await this.checkRateLimits();
          
          // Generate profile
          const profile = await this.generateProfileInternal(riasecScores, oceanScores);
          
          // Record request
          this.requestHistory.push(new Date());
          
          resolve(profile);
        } catch (error) {
          reject(error);
        }
      };

      // Add to queue based on priority
      if (priority === 'high') {
        this.requestQueue.unshift(task);
      } else {
        this.requestQueue.push(task);
      }

      this.processQueue();
    });
  }

  // Process request queue
  private async processQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const task = this.requestQueue.shift();
      if (task) {
        try {
          await task();
        } catch (error) {
          console.error('Queue task error:', error);
        }
        
        // Small delay between requests
        await this.delay(100);
      }
    }

    this.isProcessingQueue = false;
  }

  // Check rate limits
  private async checkRateLimits() {
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Clean old requests
    this.requestHistory = this.requestHistory.filter(date => date > oneDayAgo);

    const requestsLastMinute = this.requestHistory.filter(date => date > oneMinuteAgo).length;
    const requestsLastHour = this.requestHistory.filter(date => date > oneHourAgo).length;
    const requestsLastDay = this.requestHistory.length;

    // Check limits
    if (requestsLastMinute >= this.rateLimitConfig.maxRequestsPerMinute) {
      const waitTime = 60 - (now.getTime() - oneMinuteAgo.getTime()) / 1000;
      console.log(`⏳ Rate limit: waiting ${waitTime}s (minute limit)`);
      await this.delay(waitTime * 1000);
    }

    if (requestsLastHour >= this.rateLimitConfig.maxRequestsPerHour) {
      const waitTime = 3600 - (now.getTime() - oneHourAgo.getTime()) / 1000;
      console.log(`⏳ Rate limit: waiting ${waitTime}s (hour limit)`);
      await this.delay(waitTime * 1000);
    }

    if (requestsLastDay >= this.rateLimitConfig.maxRequestsPerDay) {
      throw new Error('Daily rate limit exceeded');
    }
  }

  // Internal profile generation (same as original)
  private async generateProfileInternal(
    riasecScores: RiasecScores,
    oceanScores: OceanScores
  ): Promise<CombinedProfileInterpretation> {
    const prompt = this.buildPrompt(riasecScores, oceanScores);
    
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.rateLimitConfig.retryAttempts; attempt++) {
      try {
        console.log(`🤖 Generating profile (attempt ${attempt}/${this.rateLimitConfig.retryAttempts})`);

        // Use structured output with schema like geminiService.ts
        const response = await this.genAI.models.generateContent({
          model: "gemini-2.5-pro",
          contents: prompt,
          config: {
            responseMimeType: "application/json",
            responseSchema: combinedProfileResponseSchema,
            temperature: 0.2,
            topP: 0.7,
            topK: 20
          }
        });

        const text = response.text;
        if (!text) {
          throw new Error('Empty response from Gemini AI');
        }

        const profile = JSON.parse(text);

        // Validate the response structure
        this.validateProfileStructure(profile);

        return profile;
        
      } catch (error) {
        lastError = error as Error;
        console.error(`❌ Attempt ${attempt} failed:`, error);
        
        if (attempt < this.rateLimitConfig.retryAttempts) {
          const delay = this.rateLimitConfig.retryDelay * Math.pow(this.rateLimitConfig.backoffMultiplier, attempt - 1);
          console.log(`⏳ Retrying in ${delay}ms...`);
          await this.delay(delay);
        }
      }
    }
    
    throw lastError || new Error('Profile generation failed after all retry attempts');
  }

  // Build prompt using the same format as geminiService.ts
  private buildPrompt(riasecScores: RiasecScores, oceanScores: OceanScores): string {
    // Konversi skor RIASEC dari skala 0-30 ke skala 1-100
    const convertedRiasec = {
      R: Math.round((riasecScores.R / 30) * 100),
      I: Math.round((riasecScores.I / 30) * 100),
      A: Math.round((riasecScores.A / 30) * 100),
      S: Math.round((riasecScores.S / 30) * 100),
      E: Math.round((riasecScores.E / 30) * 100),
      C: Math.round((riasecScores.C / 30) * 100)
    };

    // Konversi skor OCEAN dari skala 5-25 ke skala 1-100
    const convertedOcean = {
      O: Math.round((oceanScores.O / 25) * 100),
      C: Math.round((oceanScores.C / 25) * 100),
      E: Math.round((oceanScores.E / 25) * 100),
      A: Math.round((oceanScores.A / 25) * 100),
      N: Math.round((oceanScores.N / 25) * 100)
    };

    return `# ROLE
You are a high-precision Career Analytics Engine. Your function is to operate with unwavering objectivity, synthesizing RIASEC (Holland Codes) and Big Five (OCEAN) personality data into a clinical, data-driven career profile. Your primary directive is to report the data as it is, avoiding aspirational inflation or sugarcoating.

# ASSESSMENT DATA
## RIASEC Interest Profile (Holland Codes) - Scale 1-100:
- Realistic (R): ${convertedRiasec.R}/100 - Practical, hands-on work with tools, machines, or physical materials
- Investigative (I): ${convertedRiasec.I}/100 - Research, analysis, and intellectual problem-solving activities
- Artistic (A): ${convertedRiasec.A}/100 - Creative expression, design, and aesthetic pursuits
- Social (S): ${convertedRiasec.S}/100 - Helping, teaching, and interpersonal service activities
- Enterprising (E): ${convertedRiasec.E}/100 - Leadership, persuasion, and business-oriented activities
- Conventional (C): ${convertedRiasec.C}/100 - Organized, detail-oriented, and systematic work

## Big Five Personality Traits (OCEAN) - Scale 1-100:
- Openness (O): ${convertedOcean.O}/100 - Intellectual curiosity, creativity, and openness to new experiences
- Conscientiousness (C): ${convertedOcean.C}/100 - Organization, discipline, and goal-directed behavior
- Extraversion (E): ${convertedOcean.E}/100 - Social energy, assertiveness, and external stimulation preference
- Agreeableness (A): ${convertedOcean.A}/100 - Cooperation, trust, and concern for others' welfare
- Neuroticism (N): ${convertedOcean.N}/100 - Emotional instability, anxiety, and stress reactivity

# ANALYSIS FRAMEWORK
Conduct a comprehensive psychometric analysis that:
1. Identifies the dominant RIASEC code (highest 2-3 scores) and its career implications
2. Analyzes Big Five trait combinations and their workplace manifestations
3. Synthesizes both models to reveal personality-interest alignment patterns
4. Provides evidence-based career recommendations with specific role examples
5. Identifies authentic development opportunities based on lower scores

# CRITICAL INSTRUCTIONS
- Base ALL conclusions strictly on the numerical data provided
- Avoid generic motivational language or unfounded optimism
- Present findings as objective observations, not aspirational statements
- Include specific career roles, not just broad categories
- Acknowledge limitations and areas requiring development
- Use professional, analytical tone throughout
- Ensure all recommendations are logically derived from the scores

# OUTPUT FORMAT
Return ONLY a valid JSON object in BAHASA INDONESIA with this exact structure (no additional text, explanations, or formatting):`;
  }

  // Validate profile structure
  private validateProfileStructure(profile: any): void {
    const requiredFields = [
      'profileTitle', 'profileDescription', 'strengths', 'careerSuggestions',
      'workEnvironment', 'developmentAreas', 'personalityInsights', 'careerFit'
    ];

    for (const field of requiredFields) {
      if (!profile[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate array fields
    const arrayFields = ['strengths', 'careerSuggestions', 'developmentAreas', 'personalityInsights'];
    for (const field of arrayFields) {
      if (!Array.isArray(profile[field]) || profile[field].length === 0) {
        throw new Error(`Field ${field} must be a non-empty array`);
      }
    }
  }

  // Utility methods
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private updateAverageResponseTime(responseTime: number): void {
    const totalTime = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime;
    this.metrics.averageResponseTime = totalTime / this.metrics.totalRequests;
  }

  // Periodic cleanup
  private startPeriodicCleanup(): void {
    setInterval(async () => {
      try {
        const deleted = await enhancedDb.cleanExpiredCache();
        console.log(`🧹 Cache cleanup: removed ${deleted} expired entries`);
        this.metrics.lastCleanup = new Date();
      } catch (error) {
        console.error('Cache cleanup error:', error);
      }
    }, this.cacheConfig.cleanupInterval * 60 * 60 * 1000);
  }

  // Get performance metrics
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // Get cache statistics
  async getCacheStats() {
    const stats = await enhancedDb.getCacheStats();
    return {
      ...stats,
      hitRate: this.metrics.totalRequests > 0 ? (this.metrics.cacheHits / this.metrics.totalRequests) * 100 : 0,
    };
  }

  // Clear cache
  async clearCache(): Promise<number> {
    return await enhancedDb.clearCache();
  }

  // Update configuration
  updateConfig(config: Partial<RateLimitConfig & CacheConfig>): void {
    this.rateLimitConfig = { ...this.rateLimitConfig, ...config };
    this.cacheConfig = { ...this.cacheConfig, ...config };
  }
}
