'use client';

import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, 
  <PERSON>ltip, Legend, ResponsiveContainer, ReferenceLine 
} from 'recharts';
import { TrendingUp, Calendar, Filter } from 'lucide-react';

interface RiasecTrendData {
  date: string;
  R: number;
  I: number;
  A: number;
  S: number;
  E: number;
  C: number;
  totalAssessments: number;
}

interface RiasecTrendChartProps {
  className?: string;
  timeRange?: '7d' | '30d' | '90d' | '1y';
}

export function RiasecTrendChart({ className, timeRange = '30d' }: RiasecTrendChartProps) {
  const [data, setData] = useState<RiasecTrendData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDimensions, setSelectedDimensions] = useState({
    R: true,
    I: true,
    A: true,
    S: true,
    E: true,
    C: true,
  });

  useEffect(() => {
    fetchTrendData();
  }, [timeRange]);

  const fetchTrendData = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would fetch from an API
      // For now, generate mock trend data
      const mockData = generateMockTrendData(timeRange);
      setData(mockData);
    } catch (error) {
      console.error('Failed to fetch RIASEC trend data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateMockTrendData = (range: string): RiasecTrendData[] => {
    const days = range === '7d' ? 7 : range === '30d' ? 30 : range === '90d' ? 90 : 365;
    const data: RiasecTrendData[] = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      // Generate realistic RIASEC trends with some variation
      const baseScores = { R: 3.2, I: 3.5, A: 2.8, S: 3.8, E: 3.1, C: 3.4 };
      const seasonalVariation = Math.sin((i / days) * Math.PI * 2) * 0.3;
      const randomVariation = (Math.random() - 0.5) * 0.4;
      
      data.push({
        date: date.toISOString().split('T')[0],
        R: Math.max(1, Math.min(5, baseScores.R + seasonalVariation + randomVariation)),
        I: Math.max(1, Math.min(5, baseScores.I + seasonalVariation + randomVariation * 0.8)),
        A: Math.max(1, Math.min(5, baseScores.A + seasonalVariation + randomVariation * 1.2)),
        S: Math.max(1, Math.min(5, baseScores.S + seasonalVariation + randomVariation * 0.9)),
        E: Math.max(1, Math.min(5, baseScores.E + seasonalVariation + randomVariation * 1.1)),
        C: Math.max(1, Math.min(5, baseScores.C + seasonalVariation + randomVariation * 0.7)),
        totalAssessments: Math.floor(Math.random() * 20) + 5,
      });
    }

    return data;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    if (timeRange === '7d') {
      return date.toLocaleDateString('id-ID', { weekday: 'short', day: 'numeric' });
    } else if (timeRange === '30d') {
      return date.toLocaleDateString('id-ID', { day: 'numeric', month: 'short' });
    } else {
      return date.toLocaleDateString('id-ID', { month: 'short', year: '2-digit' });
    }
  };

  const riasecColors = {
    R: '#ef4444', // Red
    I: '#3b82f6', // Blue
    A: '#8b5cf6', // Purple
    S: '#10b981', // Green
    E: '#f59e0b', // Orange
    C: '#6b7280', // Gray
  };

  const riasecLabels = {
    R: 'Realistic',
    I: 'Investigative',
    A: 'Artistic',
    S: 'Social',
    E: 'Enterprising',
    C: 'Conventional',
  };

  const toggleDimension = (dimension: keyof typeof selectedDimensions) => {
    setSelectedDimensions(prev => ({
      ...prev,
      [dimension]: !prev[dimension],
    }));
  };

  const getAverageScore = (dimension: keyof typeof selectedDimensions) => {
    if (data.length === 0) return 0;
    const sum = data.reduce((acc, item) => acc + item[dimension], 0);
    return (sum / data.length).toFixed(2);
  };

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            RIASEC Score Trends
          </h3>
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600">
              {timeRange === '7d' ? 'Last 7 Days' : 
               timeRange === '30d' ? 'Last 30 Days' : 
               timeRange === '90d' ? 'Last 90 Days' : 'Last Year'}
            </span>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Dimension Toggles */}
        <div className="mb-6">
          <div className="flex items-center mb-3">
            <Filter className="w-4 h-4 text-gray-500 mr-2" />
            <span className="text-sm font-medium text-gray-700">Show Dimensions:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {Object.entries(riasecLabels).map(([key, label]) => (
              <button
                key={key}
                onClick={() => toggleDimension(key as keyof typeof selectedDimensions)}
                className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                  selectedDimensions[key as keyof typeof selectedDimensions]
                    ? 'text-white'
                    : 'text-gray-600 bg-gray-100 hover:bg-gray-200'
                }`}
                style={{
                  backgroundColor: selectedDimensions[key as keyof typeof selectedDimensions] 
                    ? riasecColors[key as keyof typeof riasecColors] 
                    : undefined
                }}
              >
                {label} (avg: {getAverageScore(key as keyof typeof selectedDimensions)})
              </button>
            ))}
          </div>
        </div>

        {/* Chart */}
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading trend data...</span>
          </div>
        ) : (
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={formatDate}
                  fontSize={12}
                />
                <YAxis 
                  domain={[1, 5]}
                  label={{ value: 'Score', angle: -90, position: 'insideLeft' }}
                  fontSize={12}
                />
                <Tooltip 
                  labelFormatter={(value) => `Date: ${formatDate(value as string)}`}
                  formatter={(value: number, name: string) => [
                    value.toFixed(2), 
                    riasecLabels[name as keyof typeof riasecLabels]
                  ]}
                />
                <Legend />
                
                {/* Reference line for neutral score */}
                <ReferenceLine y={3} stroke="#9ca3af" strokeDasharray="5 5" />
                
                {/* RIASEC dimension lines */}
                {Object.entries(selectedDimensions).map(([dimension, isSelected]) => 
                  isSelected && (
                    <Line
                      key={dimension}
                      type="monotone"
                      dataKey={dimension}
                      stroke={riasecColors[dimension as keyof typeof riasecColors]}
                      strokeWidth={2}
                      dot={{ r: 3 }}
                      name={riasecLabels[dimension as keyof typeof riasecLabels]}
                    />
                  )
                )}
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Summary Statistics */}
        <div className="mt-6 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {Object.entries(riasecLabels).map(([key, label]) => (
            <div 
              key={key}
              className={`p-3 rounded-lg border-2 transition-opacity ${
                selectedDimensions[key as keyof typeof selectedDimensions] 
                  ? 'opacity-100' 
                  : 'opacity-50'
              }`}
              style={{
                borderColor: riasecColors[key as keyof typeof riasecColors],
                backgroundColor: selectedDimensions[key as keyof typeof selectedDimensions] 
                  ? `${riasecColors[key as keyof typeof riasecColors]}10` 
                  : '#f9fafb'
              }}
            >
              <div className="text-center">
                <p className="text-xs font-medium text-gray-600">{label}</p>
                <p className="text-lg font-bold" style={{ color: riasecColors[key as keyof typeof riasecColors] }}>
                  {getAverageScore(key as keyof typeof selectedDimensions)}
                </p>
                <p className="text-xs text-gray-500">Average</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
