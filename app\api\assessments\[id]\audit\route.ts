import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
} from '@/lib/api-utils';
import { enhancedDb } from '@/lib/server/enhanced-database';
import { withMiddleware } from '@/lib/server/middleware';

interface AuditParams {
  params: {
    id: string;
  };
}

interface AuditEntry {
  id: string;
  action: string;
  oldValues: any;
  newValues: any;
  userId: string | null;
  timestamp: string;
  ipAddress: string | null;
  userAgent: string | null;
}

interface AuditTrailResponse {
  assessmentId: string;
  entries: AuditEntry[];
  totalEntries: number;
  page: number;
  pageSize: number;
}

// GET - Get audit trail for assessment
async function getHandler(
  request: NextRequest,
  { params }: AuditParams
) {
  const assessmentId = params.id;
  const { searchParams } = new URL(request.url);
  
  // Pagination parameters
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = Math.min(parseInt(searchParams.get('pageSize') || '50'), 100);
  const skip = (page - 1) * pageSize;

  // Filter parameters
  const action = searchParams.get('action');
  const userId = searchParams.get('userId');
  const startDate = searchParams.get('startDate');
  const endDate = searchParams.get('endDate');

  // Validate assessment ID
  if (!assessmentId || assessmentId.length < 10) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid assessment ID',
      400
    );
  }

  try {
    // Check if assessment exists
    const assessment = await enhancedDb.getAssessment(assessmentId, true);
    
    if (!assessment) {
      return createErrorResponse(
        'NOT_FOUND',
        'Assessment not found',
        404
      );
    }

    // Build filter conditions
    const whereConditions: any = {
      assessmentId,
    };

    if (action) {
      whereConditions.action = action;
    }

    if (userId) {
      whereConditions.userId = userId;
    }

    if (startDate || endDate) {
      whereConditions.timestamp = {};
      if (startDate) {
        whereConditions.timestamp.gte = new Date(startDate);
      }
      if (endDate) {
        whereConditions.timestamp.lte = new Date(endDate);
      }
    }

    // Get audit entries with pagination
    const [entries, totalCount] = await Promise.all([
      enhancedDb.getAuditTrailPaginated(whereConditions, skip, pageSize),
      enhancedDb.getAuditTrailCount(whereConditions),
    ]);

    // Transform entries for response
    const transformedEntries: AuditEntry[] = entries.map(entry => ({
      id: entry.id,
      action: entry.action,
      oldValues: entry.oldValues ? JSON.parse(entry.oldValues) : null,
      newValues: entry.newValues ? JSON.parse(entry.newValues) : null,
      userId: entry.userId,
      timestamp: entry.timestamp.toISOString(),
      ipAddress: entry.ipAddress,
      userAgent: entry.userAgent,
    }));

    const response: AuditTrailResponse = {
      assessmentId,
      entries: transformedEntries,
      totalEntries: totalCount,
      page,
      pageSize,
    };

    return createSuccessResponse(response);

  } catch (error) {
    console.error('Get audit trail error:', error);
    
    return createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to retrieve audit trail',
      500
    );
  }
}

// POST - Add manual audit entry (for admin use)
async function postHandler(
  request: NextRequest,
  { params }: AuditParams
) {
  const assessmentId = params.id;
  
  // Validate assessment ID
  if (!assessmentId || assessmentId.length < 10) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid assessment ID',
      400
    );
  }

  try {
    const body = await request.json();
    const { action, oldValues, newValues, notes } = body;

    // Validate required fields
    if (!action) {
      return createErrorResponse(
        'VALIDATION_ERROR',
        'Action is required',
        400
      );
    }

    // Check if assessment exists
    const assessment = await enhancedDb.getAssessment(assessmentId, true);
    
    if (!assessment) {
      return createErrorResponse(
        'NOT_FOUND',
        'Assessment not found',
        404
      );
    }

    // Get user info from headers (set by auth middleware)
    const userId = request.headers.get('x-user-id') || undefined;
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Create audit entry
    const auditEntry = await enhancedDb.createAuditEntry(
      assessmentId,
      action,
      oldValues,
      { ...newValues, notes },
      userId,
      { ipAddress, userAgent }
    );

    const response = {
      id: auditEntry.id,
      message: 'Audit entry created successfully',
      timestamp: auditEntry.timestamp.toISOString(),
    };

    return createSuccessResponse(response);

  } catch (error) {
    console.error('Create audit entry error:', error);
    
    return createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to create audit entry',
      500
    );
  }
}

export const GET = withMiddleware(getHandler);
export const POST = withMiddleware(postHandler);
