'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getShuffledQuestions } from '@/lib/riasecQuestions';
import { getShuffledOceanQuestions } from '@/lib/oceanQuestions';
import { calculateOceanScores } from '@/lib/profileStore';
import { RiasecScores, OceanScores, Question, OceanQuestion } from '@/lib/types';
import ProgressBar from '@/components/ProgressBar';
import QuestionCard from '@/components/QuestionCard';
import LikertScaleInput from '@/components/LikertScaleInput';
import NavigationButtons from '@/components/NavigationButtons';

const ANSWER_DELAY = 150;

export default function TestPage() {
  const router = useRouter();
  
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<{ [questionId: number]: number }>({});
  const [allQuestions, setAllQuestions] = useState<(Question | OceanQuestion)[]>([]);
  const [riasecQuestions, setRiasecQuestions] = useState<Question[]>([]);
  const [showNextButton, setShowNextButton] = useState(false);

  useEffect(() => {
    const shuffledRiasec = getShuffledQuestions();
    const shuffledOcean = getShuffledOceanQuestions();

    setRiasecQuestions(shuffledRiasec);
    setAllQuestions([...shuffledRiasec, ...shuffledOcean]);
  }, []);

  useEffect(() => {
    if (allQuestions.length > 0) {
      const currentQuestion = allQuestions[currentQuestionIndex];
      const isAnswered = answers[currentQuestion.id] !== undefined;

      if (isAnswered) {
        const timer = setTimeout(() => {
          setShowNextButton(true);
        }, ANSWER_DELAY);

        return () => clearTimeout(timer);
      } else {
        setShowNextButton(false);
      }
    }
  }, [currentQuestionIndex, answers, allQuestions]);

  if (allQuestions.length === 0) {
    return <LoadingScreen />;
  }

  const currentQuestion = allQuestions[currentQuestionIndex];
  const totalQuestions = allQuestions.length;
  const riasecCount = riasecQuestions.length;
  const isRiasecPhase = currentQuestionIndex < riasecCount;

  const handleAnswerSelect = (questionId: number, value: number) => {
    const wasAlreadyAnswered = answers[questionId] !== undefined;

    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));

    if (!wasAlreadyAnswered) {
      setTimeout(() => {
        if (currentQuestionIndex < totalQuestions - 1) {
          setCurrentQuestionIndex(prev => prev + 1);
        } else {
          const finalAnswers = {
            ...answers,
            [questionId]: value
          };
          const riasecScores = calculateRiasecScores(finalAnswers);
          const oceanScores = calculateOceanScores(finalAnswers);
          navigateToResults(riasecScores, oceanScores);
        }
      }, ANSWER_DELAY);
    }
  };

  const calculateRiasecScores = (finalAnswers: { [questionId: number]: number }): RiasecScores => {
    const riasecScores: RiasecScores = {
      R: 0,
      I: 0,
      A: 0,
      S: 0,
      E: 0,
      C: 0
    };

    riasecQuestions.forEach(question => {
      const answer = finalAnswers[question.id];
      if (answer !== undefined) {
        riasecScores[question.riasec_type] += answer;
      }
    });

    return riasecScores;
  };

  const navigateToResults = (riasecScores: RiasecScores, oceanScores: OceanScores) => {
    const queryParams = new URLSearchParams({
      r: riasecScores.R.toString(),
      i: riasecScores.I.toString(),
      a: riasecScores.A.toString(),
      s: riasecScores.S.toString(),
      e: riasecScores.E.toString(),
      c: riasecScores.C.toString(),
      o: oceanScores.O.toString(),
      ocean_c: oceanScores.C.toString(),
      ocean_e: oceanScores.E.toString(),
      ocean_a: oceanScores.A.toString(),
      n: oceanScores.N.toString()
    });

    router.push(`/result?${queryParams.toString()}`);
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleNext = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      const finalAnswers = answers;
      const riasecScores = calculateRiasecScores(finalAnswers);
      const oceanScores = calculateOceanScores(finalAnswers);
      navigateToResults(riasecScores, oceanScores);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <TestHeader isRiasecPhase={isRiasecPhase} />

        <ProgressBar 
          current={currentQuestionIndex + 1} 
          total={totalQuestions} 
        />

        <QuestionCard
          question={currentQuestion}
          questionNumber={currentQuestionIndex + 1}
          totalQuestions={totalQuestions}
        />

        <div className="bg-white rounded-lg shadow-sm p-6">
          <LikertScaleInput
            questionId={currentQuestion.id}
            selectedValue={answers[currentQuestion.id]}
            onSelect={handleAnswerSelect}
          />

          <NavigationButtons
            currentIndex={currentQuestionIndex}
            totalQuestions={totalQuestions}
            canGoNext={showNextButton}
            onPrevious={handlePrevious}
            onNext={handleNext}
          />
        </div>
      </div>
    </div>
  );
}

function LoadingScreen() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-blue-600 mx-auto mb-3"></div>
        <p className="text-gray-600 text-sm">Mempersiapkan tes...</p>
      </div>
    </div>
  );
}

interface TestHeaderProps {
  isRiasecPhase: boolean;
}

function TestHeader({ isRiasecPhase }: TestHeaderProps) {
  return (
    <div className="text-center mb-8">
      <h1 className="text-xl font-semibold text-gray-900 mb-2">
        {isRiasecPhase ? 'Tes Minat Karir' : 'Tes Kepribadian'}
      </h1>
      <p className="text-sm text-gray-600">
        {isRiasecPhase
          ? 'Jawab sesuai minat dan preferensi karir Anda'
          : 'Jawab sesuai kepribadian dan karakteristik Anda'
        }
      </p>
    </div>
  );
}