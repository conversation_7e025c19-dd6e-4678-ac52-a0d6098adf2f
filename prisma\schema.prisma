// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User model (for future user management)
model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime? // Soft delete support

  // Relations
  assessments Assessment[]
  sessions UserSession[]
  comparisons ProfileComparison[]

  // Indexes for performance
  @@index([email], name: "idx_users_email")
  @@index([deletedAt], name: "idx_users_soft_delete")
  @@map("users")
}

// Assessment model to store RIASEC and OCEAN scores
model Assessment {
  id        String   @id @default(cuid())
  resultId  String?  @unique @default(cuid()) // Unique ID for result page URL
  userId    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime? // Soft delete support

  // RIASEC Scores (0-30 each)
  riasecR Int
  riasecI Int
  riasecA Int
  riasecS Int
  riasecE Int
  riasecC Int

  // OCEAN Scores (5-25 each)
  oceanO Int
  oceanC Int
  oceanE Int
  oceanA Int
  oceanN Int

  // Relations
  user    User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  profile Profile?
  history AssessmentHistory[]
  comparisons1 ProfileComparison[] @relation("ComparisonAssessment1")
  comparisons2 ProfileComparison[] @relation("ComparisonAssessment2")

  // Indexes for performance
  @@index([userId, createdAt(sort: Desc)], name: "idx_assessments_user_created")
  @@index([resultId], name: "idx_assessments_result_id")
  @@index([createdAt(sort: Desc), userId], name: "idx_assessments_date_user")
  @@index([deletedAt], name: "idx_assessments_soft_delete")
  @@map("assessments")
}

// Profile model to store generated AI analysis
model Profile {
  id           String   @id @default(cuid())
  assessmentId String   @unique
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  deletedAt    DateTime? // Soft delete support

  // AI Generated Profile Data
  profileTitle       String
  profileDescription String
  strengths          String // JSON array as string
  careerSuggestions  String // JSON array as string
  workEnvironment    String
  developmentAreas   String // JSON array as string
  personalityInsights String // JSON array as string
  careerFit          String

  // Metadata
  generatedAt DateTime @default(now())
  aiModel     String   @default("gemini-2.5-pro")

  // Relations
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  strengthsNormalized ProfileStrength[]
  careerSuggestionsNormalized CareerSuggestion[]
  developmentAreasNormalized DevelopmentArea[]
  versions ProfileVersion[]
  recommendations CareerRecommendation[]

  // Indexes for performance
  @@index([assessmentId], name: "idx_profiles_assessment")
  @@index([deletedAt], name: "idx_profiles_soft_delete")
  @@index([generatedAt(sort: Desc)], name: "idx_profiles_generated_at")
  @@map("profiles")
}

// Audit trail for tracking changes
model AssessmentHistory {
  id           String   @id @default(cuid())
  assessmentId String
  action       String   // 'created', 'updated', 'profile_generated', 'deleted'
  oldValues    String?  // JSON string of old values
  newValues    String?  // JSON string of new values
  userId       String?  // Who made the change
  timestamp    DateTime @default(now())
  ipAddress    String?
  userAgent    String?

  // Relations
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  // Indexes
  @@index([assessmentId, timestamp(sort: Desc)], name: "idx_history_assessment_time")
  @@index([action, timestamp(sort: Desc)], name: "idx_history_action_time")
  @@index([userId, timestamp(sort: Desc)], name: "idx_history_user_time")
  @@map("assessment_history")
}

// AI Cache for performance optimization
model AiCache {
  id            String   @id @default(cuid())
  inputHash     String   @unique // Hash of RIASEC+OCEAN scores
  cachedResult  String   // JSON result from AI
  hitCount      Int      @default(1)
  createdAt     DateTime @default(now())
  lastAccessed  DateTime @default(now())
  expiresAt     DateTime? // Optional expiration

  // Indexes
  @@index([inputHash], name: "idx_cache_input_hash")
  @@index([lastAccessed(sort: Desc)], name: "idx_cache_last_accessed")
  @@index([expiresAt], name: "idx_cache_expires")
  @@map("ai_cache")
}

// Normalized storage for profile strengths
model ProfileStrength {
  id         String  @id @default(cuid())
  profileId  String
  strength   String
  orderIndex Int?
  category   String? // e.g., 'primary', 'secondary'

  // Relations
  profile Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)

  // Indexes
  @@index([profileId, orderIndex], name: "idx_strengths_profile_order")
  @@map("profile_strengths")
}

// Normalized storage for career suggestions
model CareerSuggestion {
  id               String  @id @default(cuid())
  profileId        String
  suggestion       String
  category         String? // e.g., 'primary', 'alternative', 'emerging'
  matchPercentage  Float?  // 0.0 to 1.0
  orderIndex       Int?
  reasoning        String?

  // Relations
  profile Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)

  // Indexes
  @@index([profileId, orderIndex], name: "idx_suggestions_profile_order")
  @@index([profileId, matchPercentage(sort: Desc)], name: "idx_suggestions_match")
  @@map("career_suggestions")
}

// Normalized storage for development areas
model DevelopmentArea {
  id          String  @id @default(cuid())
  profileId   String
  area        String
  priority    String? // 'high', 'medium', 'low'
  orderIndex  Int?
  description String?

  // Relations
  profile Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)

  // Indexes
  @@index([profileId, orderIndex], name: "idx_development_profile_order")
  @@map("development_areas")
}

// Analytics for reporting and insights
model AssessmentAnalytics {
  id                      String   @id @default(cuid())
  date                    DateTime @unique
  totalAssessments        Int      @default(0)
  totalProfilesGenerated  Int      @default(0)
  avgRiasecR              Float?
  avgRiasecI              Float?
  avgRiasecA              Float?
  avgRiasecS              Float?
  avgRiasecE              Float?
  avgRiasecC              Float?
  avgOceanO               Float?
  avgOceanC               Float?
  avgOceanE               Float?
  avgOceanA               Float?
  avgOceanN               Float?
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  // Indexes
  @@index([date(sort: Desc)], name: "idx_analytics_date")
  @@map("assessment_analytics")
}

// User session tracking
model UserSession {
  id                   String    @id @default(cuid())
  userId               String?
  sessionStart         DateTime  @default(now())
  sessionEnd           DateTime?
  assessmentCompleted  Boolean   @default(false)
  profileViewed        Boolean   @default(false)
  ipAddress            String?
  userAgent            String?
  referrer             String?

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  // Indexes
  @@index([userId, sessionStart(sort: Desc)], name: "idx_sessions_user_start")
  @@index([sessionStart(sort: Desc)], name: "idx_sessions_start")
  @@map("user_sessions")
}

// Profile versioning for change tracking
model ProfileVersion {
  id        String   @id @default(cuid())
  profileId String
  version   Int
  data      String   // JSON snapshot of profile data
  aiModel   String
  createdAt DateTime @default(now())
  createdBy String?  // User who triggered the change

  // Relations
  profile Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)

  // Indexes
  @@unique([profileId, version], name: "unique_profile_version")
  @@index([profileId, version(sort: Desc)], name: "idx_versions_profile_version")
  @@map("profile_versions")
}

// Profile comparison feature
model ProfileComparison {
  id             String   @id @default(cuid())
  userId         String
  assessment1Id  String
  assessment2Id  String
  comparisonData String?  // JSON analysis of differences
  createdAt      DateTime @default(now())

  // Relations
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  assessment1 Assessment @relation("ComparisonAssessment1", fields: [assessment1Id], references: [id], onDelete: Cascade)
  assessment2 Assessment @relation("ComparisonAssessment2", fields: [assessment2Id], references: [id], onDelete: Cascade)

  // Indexes
  @@index([userId, createdAt(sort: Desc)], name: "idx_comparisons_user_date")
  @@map("profile_comparisons")
}

// Enhanced career recommendations
model CareerRecommendation {
  id               String   @id @default(cuid())
  profileId        String
  careerTitle      String
  matchPercentage  Float    // 0.0 to 1.0
  reasoning        String?
  source           String   @default("ai_generated") // 'ai_generated', 'manual', 'hybrid'
  industry         String?
  experienceLevel  String?  // 'entry', 'mid', 'senior', 'executive'
  salaryRange      String?
  growthProspect   String?  // 'high', 'medium', 'low'
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  profile Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)

  // Indexes
  @@index([profileId, matchPercentage(sort: Desc)], name: "idx_recommendations_match")
  @@index([industry, experienceLevel], name: "idx_recommendations_industry_level")
  @@map("career_recommendations")
}
