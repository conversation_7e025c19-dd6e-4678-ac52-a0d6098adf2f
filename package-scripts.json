{"scripts": {"db:enhance": "node scripts/deploy-database-enhancements.js", "db:migrate": "node scripts/run-database-migration.js", "db:validate": "node scripts/add-data-validation.js", "db:test": "node scripts/test-database-enhancements.js", "db:backup": "node scripts/database-enhancement-migration.js", "db:status": "npx prisma db status", "db:studio": "npx prisma studio", "db:reset": "npx prisma migrate reset --force", "db:deploy": "npx prisma migrate deploy", "db:generate": "npx prisma generate"}}