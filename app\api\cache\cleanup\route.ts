import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
} from '@/lib/api-utils';
import { enhancedDb } from '@/lib/server/enhanced-database';
import { withMiddleware } from '@/lib/server/middleware';

// POST - Clean expired cache entries
async function postHandler(request: NextRequest) {
  try {
    // Clean expired cache entries
    const deletedCount = await enhancedDb.cleanExpiredCache();

    const response = {
      message: 'Expired cache entries cleaned successfully',
      deletedCount,
      timestamp: new Date().toISOString(),
    };

    return createSuccessResponse(response);

  } catch (error) {
    console.error('Clean expired cache error:', error);
    
    return createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to clean expired cache entries',
      500
    );
  }
}

export const POST = withMiddleware(postHandler);
