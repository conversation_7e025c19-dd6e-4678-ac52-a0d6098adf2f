const { PrismaClient } = require('../lib/generated/prisma');

async function testDatabaseEnhancements() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧪 Testing Database Enhancements');
    console.log('📋 This will test all new features and validate functionality');
    
    // Test 1: Basic CRUD operations with soft delete
    console.log('\n🔧 Test 1: Basic CRUD with Soft Delete...');
    await testBasicCrudWithSoftDelete(prisma);
    
    // Test 2: Performance indexes
    console.log('\n🔧 Test 2: Performance Indexes...');
    await testPerformanceIndexes(prisma);
    
    // Test 3: Data validation constraints
    console.log('\n🔧 Test 3: Data Validation Constraints...');
    await testDataValidation(prisma);
    
    // Test 4: Audit trail functionality
    console.log('\n🔧 Test 4: Audit Trail System...');
    await testAuditTrail(prisma);
    
    // Test 5: AI caching system
    console.log('\n🔧 Test 5: AI Caching System...');
    await testAiCaching(prisma);
    
    // Test 6: Normalized data storage
    console.log('\n🔧 Test 6: Normalized Data Storage...');
    await testNormalizedData(prisma);
    
    // Test 7: Analytics system
    console.log('\n🔧 Test 7: Analytics System...');
    await testAnalytics(prisma);
    
    // Test 8: Advanced features
    console.log('\n🔧 Test 8: Advanced Features...');
    await testAdvancedFeatures(prisma);
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('   ✓ Basic CRUD with soft delete');
    console.log('   ✓ Performance indexes');
    console.log('   ✓ Data validation constraints');
    console.log('   ✓ Audit trail system');
    console.log('   ✓ AI caching system');
    console.log('   ✓ Normalized data storage');
    console.log('   ✓ Analytics system');
    console.log('   ✓ Advanced features');
    
  } catch (error) {
    console.error('❌ Testing failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function testBasicCrudWithSoftDelete(prisma) {
  try {
    // Create test user
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User',
      },
    });
    console.log('   ✅ User created successfully');
    
    // Create test assessment
    const assessment = await prisma.assessment.create({
      data: {
        userId: user.id,
        riasecR: 20,
        riasecI: 15,
        riasecA: 10,
        riasecS: 25,
        riasecE: 18,
        riasecC: 12,
        oceanO: 20,
        oceanC: 18,
        oceanE: 15,
        oceanA: 22,
        oceanN: 10,
      },
    });
    console.log('   ✅ Assessment created successfully');
    
    // Test soft delete
    await prisma.assessment.update({
      where: { id: assessment.id },
      data: { deletedAt: new Date() },
    });
    console.log('   ✅ Soft delete working');
    
    // Test query with soft delete filter
    const activeAssessments = await prisma.assessment.findMany({
      where: { deletedAt: null },
    });
    
    const deletedAssessments = await prisma.assessment.findMany({
      where: { deletedAt: { not: null } },
    });
    
    console.log(`   ✅ Soft delete filter working (${deletedAssessments.length} deleted, ${activeAssessments.length} active)`);
    
    // Cleanup
    await prisma.assessment.delete({ where: { id: assessment.id } });
    await prisma.user.delete({ where: { id: user.id } });
    
  } catch (error) {
    console.error('   ❌ Basic CRUD test failed:', error.message);
    throw error;
  }
}

async function testPerformanceIndexes(prisma) {
  try {
    // Test index existence by running queries that would benefit from indexes
    const start = Date.now();
    
    // Query that should use user-created index
    await prisma.assessment.findMany({
      where: { userId: 'non-existent' },
      orderBy: { createdAt: 'desc' },
      take: 10,
    });
    
    // Query that should use result-id index
    await prisma.assessment.findUnique({
      where: { resultId: 'non-existent' },
    });
    
    const end = Date.now();
    console.log(`   ✅ Index queries completed in ${end - start}ms`);
    
    // Check if indexes exist in SQLite
    const indexes = await prisma.$queryRaw`
      SELECT name FROM sqlite_master 
      WHERE type='index' AND name LIKE 'idx_%'
      ORDER BY name
    `;
    
    console.log(`   ✅ Found ${indexes.length} custom indexes`);
    indexes.forEach(idx => {
      console.log(`      - ${idx.name}`);
    });
    
  } catch (error) {
    console.error('   ❌ Performance index test failed:', error.message);
    throw error;
  }
}

async function testDataValidation(prisma) {
  try {
    let validationWorking = true;
    
    // Test RIASEC validation
    try {
      await prisma.assessment.create({
        data: {
          riasecR: 35, // Invalid - should be 0-30
          riasecI: 15,
          riasecA: 15,
          riasecS: 15,
          riasecE: 15,
          riasecC: 15,
          oceanO: 15,
          oceanC: 15,
          oceanE: 15,
          oceanA: 15,
          oceanN: 15,
        },
      });
      validationWorking = false;
    } catch (error) {
      if (error.message.includes('RIASEC R score must be between 0 and 30')) {
        console.log('   ✅ RIASEC validation working');
      }
    }
    
    // Test OCEAN validation
    try {
      await prisma.assessment.create({
        data: {
          riasecR: 15,
          riasecI: 15,
          riasecA: 15,
          riasecS: 15,
          riasecE: 15,
          riasecC: 15,
          oceanO: 30, // Invalid - should be 5-25
          oceanC: 15,
          oceanE: 15,
          oceanA: 15,
          oceanN: 15,
        },
      });
      validationWorking = false;
    } catch (error) {
      if (error.message.includes('OCEAN O score must be between 5 and 25')) {
        console.log('   ✅ OCEAN validation working');
      }
    }
    
    // Test email validation
    try {
      await prisma.user.create({
        data: {
          email: 'invalid-email', // Invalid format
          name: 'Test User',
        },
      });
      validationWorking = false;
    } catch (error) {
      if (error.message.includes('Invalid email format')) {
        console.log('   ✅ Email validation working');
      }
    }
    
    if (!validationWorking) {
      throw new Error('Some validation constraints are not working');
    }
    
  } catch (error) {
    console.error('   ❌ Data validation test failed:', error.message);
    throw error;
  }
}

async function testAuditTrail(prisma) {
  try {
    // Create test assessment
    const assessment = await prisma.assessment.create({
      data: {
        riasecR: 20,
        riasecI: 15,
        riasecA: 10,
        riasecS: 25,
        riasecE: 18,
        riasecC: 12,
        oceanO: 20,
        oceanC: 18,
        oceanE: 15,
        oceanA: 22,
        oceanN: 10,
      },
    });
    
    // Create audit entry
    await prisma.assessmentHistory.create({
      data: {
        assessmentId: assessment.id,
        action: 'created',
        newValues: JSON.stringify({ test: 'data' }),
      },
    });
    
    // Retrieve audit trail
    const auditEntries = await prisma.assessmentHistory.findMany({
      where: { assessmentId: assessment.id },
    });
    
    console.log(`   ✅ Audit trail working (${auditEntries.length} entries)`);
    
    // Cleanup
    await prisma.assessmentHistory.deleteMany({
      where: { assessmentId: assessment.id },
    });
    await prisma.assessment.delete({ where: { id: assessment.id } });
    
  } catch (error) {
    console.error('   ❌ Audit trail test failed:', error.message);
    throw error;
  }
}

async function testAiCaching(prisma) {
  try {
    // Create cache entry
    const cacheEntry = await prisma.aiCache.create({
      data: {
        inputHash: 'test-hash-123',
        cachedResult: JSON.stringify({ test: 'cached data' }),
      },
    });
    
    // Retrieve cache entry
    const retrieved = await prisma.aiCache.findUnique({
      where: { inputHash: 'test-hash-123' },
    });
    
    console.log('   ✅ AI caching working');
    
    // Test hit count increment
    await prisma.aiCache.update({
      where: { id: cacheEntry.id },
      data: { hitCount: { increment: 1 } },
    });
    
    const updated = await prisma.aiCache.findUnique({
      where: { id: cacheEntry.id },
    });
    
    if (updated.hitCount === 2) {
      console.log('   ✅ Cache hit count increment working');
    }
    
    // Cleanup
    await prisma.aiCache.delete({ where: { id: cacheEntry.id } });
    
  } catch (error) {
    console.error('   ❌ AI caching test failed:', error.message);
    throw error;
  }
}

async function testNormalizedData(prisma) {
  try {
    // Create test profile
    const assessment = await prisma.assessment.create({
      data: {
        riasecR: 20, riasecI: 15, riasecA: 10, riasecS: 25, riasecE: 18, riasecC: 12,
        oceanO: 20, oceanC: 18, oceanE: 15, oceanA: 22, oceanN: 10,
      },
    });
    
    const profile = await prisma.profile.create({
      data: {
        assessmentId: assessment.id,
        profileTitle: 'Test Profile',
        profileDescription: 'Test Description',
        strengths: JSON.stringify(['Strength 1', 'Strength 2']),
        careerSuggestions: JSON.stringify(['Career 1', 'Career 2']),
        workEnvironment: 'Test Environment',
        developmentAreas: JSON.stringify(['Area 1', 'Area 2']),
        personalityInsights: JSON.stringify(['Insight 1']),
        careerFit: 'Good fit',
      },
    });
    
    // Create normalized data
    await prisma.profileStrength.create({
      data: {
        profileId: profile.id,
        strength: 'Test Strength',
        orderIndex: 1,
      },
    });
    
    await prisma.careerSuggestion.create({
      data: {
        profileId: profile.id,
        suggestion: 'Test Career',
        matchPercentage: 0.85,
        orderIndex: 1,
      },
    });
    
    // Retrieve normalized data
    const strengths = await prisma.profileStrength.findMany({
      where: { profileId: profile.id },
    });
    
    const suggestions = await prisma.careerSuggestion.findMany({
      where: { profileId: profile.id },
    });
    
    console.log(`   ✅ Normalized data working (${strengths.length} strengths, ${suggestions.length} suggestions)`);
    
    // Cleanup
    await prisma.profileStrength.deleteMany({ where: { profileId: profile.id } });
    await prisma.careerSuggestion.deleteMany({ where: { profileId: profile.id } });
    await prisma.profile.delete({ where: { id: profile.id } });
    await prisma.assessment.delete({ where: { id: assessment.id } });
    
  } catch (error) {
    console.error('   ❌ Normalized data test failed:', error.message);
    throw error;
  }
}

async function testAnalytics(prisma) {
  try {
    // Create analytics entry
    const analytics = await prisma.assessmentAnalytics.create({
      data: {
        date: new Date('2024-01-01'),
        totalAssessments: 10,
        totalProfilesGenerated: 8,
        avgRiasecR: 15.5,
      },
    });
    
    console.log('   ✅ Analytics system working');
    
    // Cleanup
    await prisma.assessmentAnalytics.delete({ where: { id: analytics.id } });
    
  } catch (error) {
    console.error('   ❌ Analytics test failed:', error.message);
    throw error;
  }
}

async function testAdvancedFeatures(prisma) {
  try {
    // Create test data
    const user = await prisma.user.create({
      data: { email: '<EMAIL>', name: 'Test User' },
    });
    
    const assessment1 = await prisma.assessment.create({
      data: {
        userId: user.id,
        riasecR: 20, riasecI: 15, riasecA: 10, riasecS: 25, riasecE: 18, riasecC: 12,
        oceanO: 20, oceanC: 18, oceanE: 15, oceanA: 22, oceanN: 10,
      },
    });
    
    const assessment2 = await prisma.assessment.create({
      data: {
        userId: user.id,
        riasecR: 18, riasecI: 20, riasecA: 15, riasecS: 20, riasecE: 15, riasecC: 10,
        oceanO: 22, oceanC: 20, oceanE: 18, oceanA: 20, oceanN: 12,
      },
    });
    
    // Test profile comparison
    const comparison = await prisma.profileComparison.create({
      data: {
        userId: user.id,
        assessment1Id: assessment1.id,
        assessment2Id: assessment2.id,
        comparisonData: JSON.stringify({ differences: 'test' }),
      },
    });
    
    console.log('   ✅ Profile comparison working');
    
    // Test user session
    const session = await prisma.userSession.create({
      data: {
        userId: user.id,
        assessmentCompleted: true,
      },
    });
    
    console.log('   ✅ User session tracking working');
    
    // Cleanup
    await prisma.profileComparison.delete({ where: { id: comparison.id } });
    await prisma.userSession.delete({ where: { id: session.id } });
    await prisma.assessment.delete({ where: { id: assessment1.id } });
    await prisma.assessment.delete({ where: { id: assessment2.id } });
    await prisma.user.delete({ where: { id: user.id } });
    
  } catch (error) {
    console.error('   ❌ Advanced features test failed:', error.message);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  testDatabaseEnhancements()
    .then(() => {
      console.log('🎉 All database enhancement tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Database enhancement tests failed:', error);
      process.exit(1);
    });
}

module.exports = { testDatabaseEnhancements };
