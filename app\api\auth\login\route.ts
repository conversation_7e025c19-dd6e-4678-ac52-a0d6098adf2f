import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
} from '@/lib/api-utils';
import { LoginRequest, LoginResponse, UserWithAssessments } from '@/lib/auth-types';
import { db } from '@/lib/server/database';
import { withMiddleware, sanitizeInput } from '@/lib/server/middleware';

async function postHandler(req: NextRequest) {
  const body: LoginRequest = await req.json();

  // Validate required fields
  if (!body.email) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Email is required',
      400
    );
  }

  // Sanitize input
  const email = sanitizeInput(body.email.toLowerCase().trim());

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid email format',
      400
    );
  }

  try {
    // Get user by email
    const dbUser = await db.getUserByEmail(email);
    
    if (!dbUser) {
      return createErrorResponse(
        'USER_NOT_FOUND',
        'User with this email does not exist',
        404
      );
    }

    // Transform assessments data
    const assessments = dbUser.assessments.map(assessment => ({
      id: assessment.id,
      resultId: assessment.resultId || assessment.id,
      createdAt: assessment.createdAt.toISOString(),
      profileGenerated: !!assessment.profile,
      profile: assessment.profile ? {
        profileTitle: assessment.profile.profileTitle,
        profileDescription: assessment.profile.profileDescription,
      } : undefined,
    }));

    const user: UserWithAssessments = {
      id: dbUser.id,
      email: dbUser.email!,
      name: dbUser.name!,
      createdAt: dbUser.createdAt.toISOString(),
      updatedAt: dbUser.updatedAt.toISOString(),
      assessments,
    };

    const response: LoginResponse = {
      user,
      message: 'Login successful',
    };

    // Create session cookie
    const sessionData = {
      userId: user.id,
      email: user.email,
      loginTime: new Date().toISOString(),
    };

    const nextResponse = createSuccessResponse(response);

    // Set session cookie (7 days expiry)
    nextResponse.cookies.set('atma_session', JSON.stringify(sessionData), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/',
    });

    return nextResponse;
  } catch (error) {
    console.error('Login error:', error);
    
    return createErrorResponse(
      'LOGIN_ERROR',
      'Failed to login user',
      500
    );
  }
}

export const POST = withMiddleware(postHandler);
