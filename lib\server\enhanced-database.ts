import { PrismaClient } from '../generated/prisma';
import { RiasecScores, OceanScores } from '../types';
import crypto from 'crypto';

// Initialize Prisma client
const prisma = new PrismaClient();

// Enhanced Database Service with new features
export class EnhancedDatabaseService {
  private static instance: EnhancedDatabaseService;

  static getInstance(): EnhancedDatabaseService {
    if (!EnhancedDatabaseService.instance) {
      EnhancedDatabaseService.instance = new EnhancedDatabaseService();
    }
    return EnhancedDatabaseService.instance;
  }

  // ==================== ASSESSMENT OPERATIONS ====================

  // Create assessment with audit trail
  async createAssessment(
    riasecScores: RiasecScores,
    oceanScores: OceanScores,
    userId?: string,
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
    }
  ) {
    const assessment = await prisma.assessment.create({
      data: {
        userId,
        riasecR: riasecScores.R,
        riasecI: riasecScores.I,
        riasecA: riasecScores.A,
        riasecS: riasecScores.S,
        riasecE: riasecScores.E,
        riasecC: riasecScores.C,
        oceanO: oceanScores.O,
        oceanC: oceanScores.C,
        oceanE: oceanScores.E,
        oceanA: oceanScores.A,
        oceanN: oceanScores.N,
      },
    });

    // Create audit trail entry
    await this.createAuditEntry(
      assessment.id,
      'created',
      null,
      { riasecScores, oceanScores },
      userId,
      metadata
    );

    return assessment;
  }

  // Get assessment with soft delete support
  async getAssessment(assessmentId: string, includeDeleted = false) {
    return await prisma.assessment.findUnique({
      where: { 
        id: assessmentId,
        ...(includeDeleted ? {} : { deletedAt: null })
      },
      include: { 
        profile: {
          where: includeDeleted ? {} : { deletedAt: null }
        }
      },
    });
  }

  // Get assessment with scores (backward compatibility)
  async getAssessmentWithScores(assessmentId: string) {
    const assessment = await this.getAssessment(assessmentId);
    if (!assessment) return null;

    return {
      ...assessment,
      riasecScores: {
        R: assessment.riasecR,
        I: assessment.riasecI,
        A: assessment.riasecA,
        S: assessment.riasecS,
        E: assessment.riasecE,
        C: assessment.riasecC,
      },
      oceanScores: {
        O: assessment.oceanO,
        C: assessment.oceanC,
        E: assessment.oceanE,
        A: assessment.oceanA,
        N: assessment.oceanN,
      },
    };
  }

  // Get assessment with profile (backward compatibility)
  async getAssessmentWithProfile(assessmentId: string) {
    const assessment = await this.getAssessmentWithScores(assessmentId);
    if (!assessment) return null;

    const profile = await this.getProfile(assessmentId);

    return {
      assessment,
      profile,
    };
  }

  // Mark profile as generated (backward compatibility)
  async markProfileGenerated(assessmentId: string) {
    // This is now handled automatically when creating a profile
    // Just return success for backward compatibility
    return { success: true };
  }

  // Soft delete assessment
  async softDeleteAssessment(assessmentId: string, userId?: string) {
    const assessment = await prisma.assessment.update({
      where: { id: assessmentId },
      data: { deletedAt: new Date() },
    });

    // Create audit trail entry
    await this.createAuditEntry(
      assessmentId,
      'deleted',
      null,
      { deletedAt: assessment.deletedAt },
      userId
    );

    return assessment;
  }

  // Restore soft deleted assessment
  async restoreAssessment(assessmentId: string, userId?: string) {
    const assessment = await prisma.assessment.update({
      where: { id: assessmentId },
      data: { deletedAt: null },
    });

    // Create audit trail entry
    await this.createAuditEntry(
      assessmentId,
      'restored',
      null,
      { restoredAt: new Date() },
      userId
    );

    return assessment;
  }

  // ==================== AI CACHING OPERATIONS ====================

  // Generate cache key from scores
  private generateCacheKey(riasecScores: RiasecScores, oceanScores: OceanScores): string {
    const input = JSON.stringify({ riasecScores, oceanScores });
    return crypto.createHash('sha256').update(input).digest('hex');
  }

  // Check AI cache
  async checkAiCache(riasecScores: RiasecScores, oceanScores: OceanScores) {
    const inputHash = this.generateCacheKey(riasecScores, oceanScores);
    
    const cached = await prisma.aiCache.findUnique({
      where: { inputHash },
    });

    if (cached) {
      // Update hit count and last accessed
      await prisma.aiCache.update({
        where: { id: cached.id },
        data: {
          hitCount: { increment: 1 },
          lastAccessed: new Date(),
        },
      });

      return JSON.parse(cached.cachedResult);
    }

    return null;
  }

  // Store AI result in cache
  async storeAiCache(
    riasecScores: RiasecScores,
    oceanScores: OceanScores,
    result: any,
    expiresInHours = 24 * 7 // 1 week default
  ) {
    const inputHash = this.generateCacheKey(riasecScores, oceanScores);
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expiresInHours);

    return await prisma.aiCache.upsert({
      where: { inputHash },
      update: {
        cachedResult: JSON.stringify(result),
        hitCount: { increment: 1 },
        lastAccessed: new Date(),
        expiresAt,
      },
      create: {
        inputHash,
        cachedResult: JSON.stringify(result),
        expiresAt,
      },
    });
  }

  // Clean expired cache entries
  async cleanExpiredCache() {
    const deleted = await prisma.aiCache.deleteMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    });

    return deleted.count;
  }

  // Get cache statistics
  async getCacheStats() {
    const [totalEntries, totalHits, oldestEntry, newestEntry] = await Promise.all([
      prisma.aiCache.count(),
      prisma.aiCache.aggregate({
        _sum: { hitCount: true },
      }),
      prisma.aiCache.findFirst({
        orderBy: { createdAt: 'asc' },
        select: { createdAt: true },
      }),
      prisma.aiCache.findFirst({
        orderBy: { createdAt: 'desc' },
        select: { createdAt: true },
      }),
    ]);

    return {
      totalEntries,
      totalHits: totalHits._sum.hitCount || 0,
      oldestEntry: oldestEntry?.createdAt || null,
      newestEntry: newestEntry?.createdAt || null,
    };
  }

  // Clear all cache
  async clearCache() {
    const deleted = await prisma.aiCache.deleteMany({});
    return deleted.count;
  }

  // Get total counts for overview
  async getTotalAssessments() {
    return await prisma.assessment.count({
      where: { deletedAt: null },
    });
  }

  async getTotalProfiles() {
    return await prisma.profile.count({
      where: { deletedAt: null },
    });
  }

  async getTotalUsers() {
    return await prisma.user.count({
      where: { deletedAt: null },
    });
  }

  // Get daily analytics within date range
  async getDailyAnalytics(startDate: Date, endDate: Date) {
    return await prisma.assessmentAnalytics.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: { date: 'asc' },
    });
  }

  // ==================== PROFILE OPERATIONS ====================

  // Create profile with versioning
  async createProfile(
    assessmentId: string,
    profileData: any,
    userId?: string
  ) {
    const profile = await prisma.profile.create({
      data: {
        assessmentId,
        profileTitle: profileData.profileTitle,
        profileDescription: profileData.profileDescription,
        strengths: JSON.stringify(profileData.strengths),
        careerSuggestions: JSON.stringify(profileData.careerSuggestions),
        workEnvironment: profileData.workEnvironment,
        developmentAreas: JSON.stringify(profileData.developmentAreas),
        personalityInsights: JSON.stringify(profileData.personalityInsights),
        careerFit: profileData.careerFit,
      },
    });

    // Create initial version
    await this.createProfileVersion(profile.id, profileData, 1, userId);

    // Create normalized data
    await this.createNormalizedProfileData(profile.id, profileData);

    // Create audit trail entry
    await this.createAuditEntry(
      assessmentId,
      'profile_generated',
      null,
      { profileId: profile.id },
      userId
    );

    return profile;
  }

  // Create profile version
  async createProfileVersion(
    profileId: string,
    profileData: any,
    version: number,
    createdBy?: string
  ) {
    return await prisma.profileVersion.create({
      data: {
        profileId,
        version,
        data: JSON.stringify(profileData),
        aiModel: profileData.aiModel || 'gemini-2.5-pro',
        createdBy,
      },
    });
  }

  // Create normalized profile data
  async createNormalizedProfileData(profileId: string, profileData: any) {
    // Create strengths
    if (profileData.strengths && Array.isArray(profileData.strengths)) {
      const strengthsData = profileData.strengths.map((strength: string, index: number) => ({
        profileId,
        strength,
        orderIndex: index,
        category: index < 3 ? 'primary' : 'secondary',
      }));

      await prisma.profileStrength.createMany({
        data: strengthsData,
      });
    }

    // Create career suggestions
    if (profileData.careerSuggestions && Array.isArray(profileData.careerSuggestions)) {
      const suggestionsData = profileData.careerSuggestions.map((suggestion: string, index: number) => ({
        profileId,
        suggestion,
        orderIndex: index,
        category: index < 5 ? 'primary' : 'alternative',
        matchPercentage: Math.max(0.6, 1 - (index * 0.1)), // Decreasing match percentage
      }));

      await prisma.careerSuggestion.createMany({
        data: suggestionsData,
      });
    }

    // Create development areas
    if (profileData.developmentAreas && Array.isArray(profileData.developmentAreas)) {
      const developmentData = profileData.developmentAreas.map((area: string, index: number) => ({
        profileId,
        area,
        orderIndex: index,
        priority: index < 2 ? 'high' : index < 4 ? 'medium' : 'low',
      }));

      await prisma.developmentArea.createMany({
        data: developmentData,
      });
    }
  }

  // ==================== AUDIT TRAIL OPERATIONS ====================

  // Create audit entry
  async createAuditEntry(
    assessmentId: string,
    action: string,
    oldValues: any,
    newValues: any,
    userId?: string,
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
    }
  ) {
    return await prisma.assessmentHistory.create({
      data: {
        assessmentId,
        action,
        oldValues: oldValues ? JSON.stringify(oldValues) : null,
        newValues: newValues ? JSON.stringify(newValues) : null,
        userId,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
      },
    });
  }

  // Get audit trail for assessment
  async getAuditTrail(assessmentId: string) {
    return await prisma.assessmentHistory.findMany({
      where: { assessmentId },
      orderBy: { timestamp: 'desc' },
    });
  }

  // Get audit trail with pagination
  async getAuditTrailPaginated(whereConditions: any, skip: number, take: number) {
    return await prisma.assessmentHistory.findMany({
      where: whereConditions,
      orderBy: { timestamp: 'desc' },
      skip,
      take,
    });
  }

  // Get audit trail count
  async getAuditTrailCount(whereConditions: any) {
    return await prisma.assessmentHistory.count({
      where: whereConditions,
    });
  }

  // Get audit trail summary
  async getAuditTrailSummary(assessmentId: string) {
    const summary = await prisma.assessmentHistory.groupBy({
      by: ['action'],
      where: { assessmentId },
      _count: {
        action: true,
      },
    });

    return summary.map(item => ({
      action: item.action,
      count: item._count.action,
    }));
  }

  // ==================== ANALYTICS OPERATIONS ====================

  // Update daily analytics
  async updateDailyAnalytics(date: Date) {
    const dateStr = date.toISOString().split('T')[0];
    
    // Get daily stats
    const assessmentCount = await prisma.assessment.count({
      where: {
        createdAt: {
          gte: new Date(dateStr),
          lt: new Date(new Date(dateStr).getTime() + 24 * 60 * 60 * 1000),
        },
        deletedAt: null,
      },
    });

    const profileCount = await prisma.profile.count({
      where: {
        createdAt: {
          gte: new Date(dateStr),
          lt: new Date(new Date(dateStr).getTime() + 24 * 60 * 60 * 1000),
        },
        deletedAt: null,
      },
    });

    // Calculate average scores
    const avgScores = await prisma.assessment.aggregate({
      where: {
        createdAt: {
          gte: new Date(dateStr),
          lt: new Date(new Date(dateStr).getTime() + 24 * 60 * 60 * 1000),
        },
        deletedAt: null,
      },
      _avg: {
        riasecR: true,
        riasecI: true,
        riasecA: true,
        riasecS: true,
        riasecE: true,
        riasecC: true,
        oceanO: true,
        oceanC: true,
        oceanE: true,
        oceanA: true,
        oceanN: true,
      },
    });

    // Upsert analytics record
    return await prisma.assessmentAnalytics.upsert({
      where: { date: new Date(dateStr) },
      update: {
        totalAssessments: assessmentCount,
        totalProfilesGenerated: profileCount,
        avgRiasecR: avgScores._avg.riasecR,
        avgRiasecI: avgScores._avg.riasecI,
        avgRiasecA: avgScores._avg.riasecA,
        avgRiasecS: avgScores._avg.riasecS,
        avgRiasecE: avgScores._avg.riasecE,
        avgRiasecC: avgScores._avg.riasecC,
        avgOceanO: avgScores._avg.oceanO,
        avgOceanC: avgScores._avg.oceanC,
        avgOceanE: avgScores._avg.oceanE,
        avgOceanA: avgScores._avg.oceanA,
        avgOceanN: avgScores._avg.oceanN,
        updatedAt: new Date(),
      },
      create: {
        date: new Date(dateStr),
        totalAssessments: assessmentCount,
        totalProfilesGenerated: profileCount,
        avgRiasecR: avgScores._avg.riasecR,
        avgRiasecI: avgScores._avg.riasecI,
        avgRiasecA: avgScores._avg.riasecA,
        avgRiasecS: avgScores._avg.riasecS,
        avgRiasecE: avgScores._avg.riasecE,
        avgRiasecC: avgScores._avg.riasecC,
        avgOceanO: avgScores._avg.oceanO,
        avgOceanC: avgScores._avg.oceanC,
        avgOceanE: avgScores._avg.oceanE,
        avgOceanA: avgScores._avg.oceanA,
        avgOceanN: avgScores._avg.oceanN,
      },
    });
  }

  // ==================== USER SESSION TRACKING ====================

  // Create user session
  async createUserSession(
    userId?: string,
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
      referrer?: string;
    }
  ) {
    return await prisma.userSession.create({
      data: {
        userId,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        referrer: metadata?.referrer,
      },
    });
  }

  // Update session completion
  async updateSessionCompletion(
    sessionId: string,
    assessmentCompleted?: boolean,
    profileViewed?: boolean
  ) {
    return await prisma.userSession.update({
      where: { id: sessionId },
      data: {
        ...(assessmentCompleted !== undefined && { assessmentCompleted }),
        ...(profileViewed !== undefined && { profileViewed }),
        sessionEnd: new Date(),
      },
    });
  }

  // ==================== UTILITY METHODS ====================

  // Health check
  async healthCheck() {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return { status: 'healthy', timestamp: new Date() };
    } catch (error) {
      return { status: 'unhealthy', error: error.message, timestamp: new Date() };
    }
  }

  // Disconnect
  async disconnect() {
    await prisma.$disconnect();
  }
}

// Export singleton instance
export const enhancedDb = EnhancedDatabaseService.getInstance();
