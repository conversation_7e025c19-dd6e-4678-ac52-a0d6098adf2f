-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "email" TEXT,
    "name" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "deletedAt" DATETIME
);

-- CreateTable
CREATE TABLE "assessments" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "resultId" TEXT,
    "userId" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "deletedAt" DATETIME,
    "riasecR" INTEGER NOT NULL,
    "riasecI" INTEGER NOT NULL,
    "riasecA" INTEGER NOT NULL,
    "riasecS" INTEGER NOT NULL,
    "riasecE" INTEGER NOT NULL,
    "riasecC" INTEGER NOT NULL,
    "oceanO" INTEGER NOT NULL,
    "oceanC" INTEGER NOT NULL,
    "oceanE" INTEGER NOT NULL,
    "oceanA" INTEGER NOT NULL,
    "oceanN" INTEGER NOT NULL,
    CONSTRAINT "assessments_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "profiles" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "assessmentId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "deletedAt" DATETIME,
    "profileTitle" TEXT NOT NULL,
    "profileDescription" TEXT NOT NULL,
    "strengths" TEXT NOT NULL,
    "careerSuggestions" TEXT NOT NULL,
    "workEnvironment" TEXT NOT NULL,
    "developmentAreas" TEXT NOT NULL,
    "personalityInsights" TEXT NOT NULL,
    "careerFit" TEXT NOT NULL,
    "generatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "aiModel" TEXT NOT NULL DEFAULT 'gemini-2.5-pro',
    CONSTRAINT "profiles_assessmentId_fkey" FOREIGN KEY ("assessmentId") REFERENCES "assessments" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "assessment_history" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "assessmentId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "oldValues" TEXT,
    "newValues" TEXT,
    "userId" TEXT,
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    CONSTRAINT "assessment_history_assessmentId_fkey" FOREIGN KEY ("assessmentId") REFERENCES "assessments" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "ai_cache" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "inputHash" TEXT NOT NULL,
    "cachedResult" TEXT NOT NULL,
    "hitCount" INTEGER NOT NULL DEFAULT 1,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastAccessed" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" DATETIME
);

-- CreateTable
CREATE TABLE "profile_strengths" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "profileId" TEXT NOT NULL,
    "strength" TEXT NOT NULL,
    "orderIndex" INTEGER,
    "category" TEXT,
    CONSTRAINT "profile_strengths_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "profiles" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "career_suggestions" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "profileId" TEXT NOT NULL,
    "suggestion" TEXT NOT NULL,
    "category" TEXT,
    "matchPercentage" REAL,
    "orderIndex" INTEGER,
    "reasoning" TEXT,
    CONSTRAINT "career_suggestions_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "profiles" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "development_areas" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "profileId" TEXT NOT NULL,
    "area" TEXT NOT NULL,
    "priority" TEXT,
    "orderIndex" INTEGER,
    "description" TEXT,
    CONSTRAINT "development_areas_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "profiles" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "assessment_analytics" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "date" DATETIME NOT NULL,
    "totalAssessments" INTEGER NOT NULL DEFAULT 0,
    "totalProfilesGenerated" INTEGER NOT NULL DEFAULT 0,
    "avgRiasecR" REAL,
    "avgRiasecI" REAL,
    "avgRiasecA" REAL,
    "avgRiasecS" REAL,
    "avgRiasecE" REAL,
    "avgRiasecC" REAL,
    "avgOceanO" REAL,
    "avgOceanC" REAL,
    "avgOceanE" REAL,
    "avgOceanA" REAL,
    "avgOceanN" REAL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "user_sessions" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT,
    "sessionStart" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "sessionEnd" DATETIME,
    "assessmentCompleted" BOOLEAN NOT NULL DEFAULT false,
    "profileViewed" BOOLEAN NOT NULL DEFAULT false,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "referrer" TEXT,
    CONSTRAINT "user_sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "profile_versions" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "profileId" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "data" TEXT NOT NULL,
    "aiModel" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" TEXT,
    CONSTRAINT "profile_versions_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "profiles" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "profile_comparisons" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "assessment1Id" TEXT NOT NULL,
    "assessment2Id" TEXT NOT NULL,
    "comparisonData" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "profile_comparisons_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "profile_comparisons_assessment1Id_fkey" FOREIGN KEY ("assessment1Id") REFERENCES "assessments" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "profile_comparisons_assessment2Id_fkey" FOREIGN KEY ("assessment2Id") REFERENCES "assessments" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "career_recommendations" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "profileId" TEXT NOT NULL,
    "careerTitle" TEXT NOT NULL,
    "matchPercentage" REAL NOT NULL,
    "reasoning" TEXT,
    "source" TEXT NOT NULL DEFAULT 'ai_generated',
    "industry" TEXT,
    "experienceLevel" TEXT,
    "salaryRange" TEXT,
    "growthProspect" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "career_recommendations_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "profiles" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE INDEX "idx_users_email" ON "users"("email");

-- CreateIndex
CREATE INDEX "idx_users_soft_delete" ON "users"("deletedAt");

-- CreateIndex
CREATE UNIQUE INDEX "assessments_resultId_key" ON "assessments"("resultId");

-- CreateIndex
CREATE INDEX "idx_assessments_user_created" ON "assessments"("userId", "createdAt" DESC);

-- CreateIndex
CREATE INDEX "idx_assessments_result_id" ON "assessments"("resultId");

-- CreateIndex
CREATE INDEX "idx_assessments_date_user" ON "assessments"("createdAt" DESC, "userId");

-- CreateIndex
CREATE INDEX "idx_assessments_soft_delete" ON "assessments"("deletedAt");

-- CreateIndex
CREATE UNIQUE INDEX "profiles_assessmentId_key" ON "profiles"("assessmentId");

-- CreateIndex
CREATE INDEX "idx_profiles_assessment" ON "profiles"("assessmentId");

-- CreateIndex
CREATE INDEX "idx_profiles_soft_delete" ON "profiles"("deletedAt");

-- CreateIndex
CREATE INDEX "idx_profiles_generated_at" ON "profiles"("generatedAt" DESC);

-- CreateIndex
CREATE INDEX "idx_history_assessment_time" ON "assessment_history"("assessmentId", "timestamp" DESC);

-- CreateIndex
CREATE INDEX "idx_history_action_time" ON "assessment_history"("action", "timestamp" DESC);

-- CreateIndex
CREATE INDEX "idx_history_user_time" ON "assessment_history"("userId", "timestamp" DESC);

-- CreateIndex
CREATE UNIQUE INDEX "ai_cache_inputHash_key" ON "ai_cache"("inputHash");

-- CreateIndex
CREATE INDEX "idx_cache_input_hash" ON "ai_cache"("inputHash");

-- CreateIndex
CREATE INDEX "idx_cache_last_accessed" ON "ai_cache"("lastAccessed" DESC);

-- CreateIndex
CREATE INDEX "idx_cache_expires" ON "ai_cache"("expiresAt");

-- CreateIndex
CREATE INDEX "idx_strengths_profile_order" ON "profile_strengths"("profileId", "orderIndex");

-- CreateIndex
CREATE INDEX "idx_suggestions_profile_order" ON "career_suggestions"("profileId", "orderIndex");

-- CreateIndex
CREATE INDEX "idx_suggestions_match" ON "career_suggestions"("profileId", "matchPercentage" DESC);

-- CreateIndex
CREATE INDEX "idx_development_profile_order" ON "development_areas"("profileId", "orderIndex");

-- CreateIndex
CREATE UNIQUE INDEX "assessment_analytics_date_key" ON "assessment_analytics"("date");

-- CreateIndex
CREATE INDEX "idx_analytics_date" ON "assessment_analytics"("date" DESC);

-- CreateIndex
CREATE INDEX "idx_sessions_user_start" ON "user_sessions"("userId", "sessionStart" DESC);

-- CreateIndex
CREATE INDEX "idx_sessions_start" ON "user_sessions"("sessionStart" DESC);

-- CreateIndex
CREATE INDEX "idx_versions_profile_version" ON "profile_versions"("profileId", "version" DESC);

-- CreateIndex
CREATE UNIQUE INDEX "profile_versions_profileId_version_key" ON "profile_versions"("profileId", "version");

-- CreateIndex
CREATE INDEX "idx_comparisons_user_date" ON "profile_comparisons"("userId", "createdAt" DESC);

-- CreateIndex
CREATE INDEX "idx_recommendations_match" ON "career_recommendations"("profileId", "matchPercentage" DESC);

-- CreateIndex
CREATE INDEX "idx_recommendations_industry_level" ON "career_recommendations"("industry", "experienceLevel");
