'use client';

import React, { useState, useEffect } from 'react';
import { 
  LineChart, Line, AreaChart, Area, XAxis, YAxis, 
  CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer 
} from 'recharts';
import { Clock, Zap, TrendingUp, AlertTriangle, RefreshCw } from 'lucide-react';

interface PerformanceData {
  timestamp: string;
  responseTime: number;
  cacheHitRate: number;
  errorRate: number;
  throughput: number;
}

interface PerformanceMetricsProps {
  className?: string;
}

export function PerformanceMetrics({ className }: PerformanceMetricsProps) {
  const [data, setData] = useState<PerformanceData[]>([]);
  const [timeRange, setTimeRange] = useState('24h');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const fetchPerformanceData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/analytics/performance?range=${timeRange}`);
      if (!response.ok) {
        throw new Error('Failed to fetch performance data');
      }

      const result = await response.json();
      setData(result.data || []);
      setLastUpdate(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch performance data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchPerformanceData, 30000);
    return () => clearInterval(interval);
  }, [timeRange]);

  const formatTimestamp = (timestamp: string) => {
    return new Intl.DateTimeFormat('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      day: '2-digit',
      month: 'short',
    }).format(new Date(timestamp));
  };

  const getLatestMetrics = () => {
    if (data.length === 0) return null;
    
    const latest = data[data.length - 1];
    const previous = data.length > 1 ? data[data.length - 2] : latest;
    
    return {
      responseTime: {
        value: latest.responseTime,
        change: latest.responseTime - previous.responseTime,
        trend: latest.responseTime < previous.responseTime ? 'up' : 'down'
      },
      cacheHitRate: {
        value: latest.cacheHitRate,
        change: latest.cacheHitRate - previous.cacheHitRate,
        trend: latest.cacheHitRate > previous.cacheHitRate ? 'up' : 'down'
      },
      errorRate: {
        value: latest.errorRate,
        change: latest.errorRate - previous.errorRate,
        trend: latest.errorRate < previous.errorRate ? 'up' : 'down'
      },
      throughput: {
        value: latest.throughput,
        change: latest.throughput - previous.throughput,
        trend: latest.throughput > previous.throughput ? 'up' : 'down'
      }
    };
  };

  const metrics = getLatestMetrics();

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Performance Metrics
          </h3>
          <div className="flex items-center space-x-4">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="1h">Last Hour</option>
              <option value="6h">Last 6 Hours</option>
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
            </select>
            <button
              onClick={fetchPerformanceData}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh data"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
        <p className="text-sm text-gray-500 mt-1">
          Last updated: {lastUpdate.toLocaleTimeString('id-ID')}
        </p>
      </div>

      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <div className="flex">
            <AlertTriangle className="w-5 h-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="p-6">
        {/* Key Metrics Cards */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Response Time</p>
                  <p className="text-2xl font-bold text-blue-900">{metrics.responseTime.value.toFixed(0)}ms</p>
                </div>
                <Clock className="w-8 h-8 text-blue-500" />
              </div>
              <div className={`flex items-center mt-2 text-sm ${
                metrics.responseTime.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                <span>{metrics.responseTime.change > 0 ? '+' : ''}{metrics.responseTime.change.toFixed(0)}ms</span>
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Cache Hit Rate</p>
                  <p className="text-2xl font-bold text-green-900">{(metrics.cacheHitRate * 100).toFixed(1)}%</p>
                </div>
                <Zap className="w-8 h-8 text-green-500" />
              </div>
              <div className={`flex items-center mt-2 text-sm ${
                metrics.cacheHitRate.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                <span>{metrics.cacheHitRate.change > 0 ? '+' : ''}{(metrics.cacheHitRate.change * 100).toFixed(1)}%</span>
              </div>
            </div>

            <div className="bg-red-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-red-600">Error Rate</p>
                  <p className="text-2xl font-bold text-red-900">{(metrics.errorRate * 100).toFixed(2)}%</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-red-500" />
              </div>
              <div className={`flex items-center mt-2 text-sm ${
                metrics.errorRate.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                <span>{metrics.errorRate.change > 0 ? '+' : ''}{(metrics.errorRate.change * 100).toFixed(2)}%</span>
              </div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">Throughput</p>
                  <p className="text-2xl font-bold text-purple-900">{metrics.throughput.toFixed(0)}/min</p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-500" />
              </div>
              <div className={`flex items-center mt-2 text-sm ${
                metrics.throughput.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                <span>{metrics.throughput.change > 0 ? '+' : ''}{metrics.throughput.change.toFixed(0)}/min</span>
              </div>
            </div>
          </div>
        )}

        {/* Performance Charts */}
        {data.length > 0 ? (
          <div className="space-y-6">
            {/* Response Time Chart */}
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Response Time Trend</h4>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={formatTimestamp}
                      fontSize={12}
                    />
                    <YAxis 
                      label={{ value: 'Response Time (ms)', angle: -90, position: 'insideLeft' }}
                      fontSize={12}
                    />
                    <Tooltip 
                      labelFormatter={(value) => `Time: ${formatTimestamp(value as string)}`}
                      formatter={(value: number) => [`${value.toFixed(0)}ms`, 'Response Time']}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="responseTime" 
                      stroke="#3b82f6" 
                      strokeWidth={2}
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Cache Hit Rate and Error Rate Chart */}
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Cache Performance & Error Rate</h4>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={formatTimestamp}
                      fontSize={12}
                    />
                    <YAxis 
                      label={{ value: 'Rate (%)', angle: -90, position: 'insideLeft' }}
                      fontSize={12}
                    />
                    <Tooltip 
                      labelFormatter={(value) => `Time: ${formatTimestamp(value as string)}`}
                      formatter={(value: number, name: string) => [
                        `${(value * 100).toFixed(1)}%`, 
                        name === 'cacheHitRate' ? 'Cache Hit Rate' : 'Error Rate'
                      ]}
                    />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="cacheHitRate" 
                      stroke="#10b981" 
                      strokeWidth={2}
                      dot={false}
                      name="Cache Hit Rate"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="errorRate" 
                      stroke="#ef4444" 
                      strokeWidth={2}
                      dot={false}
                      name="Error Rate"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Throughput Chart */}
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Request Throughput</h4>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={formatTimestamp}
                      fontSize={12}
                    />
                    <YAxis 
                      label={{ value: 'Requests/min', angle: -90, position: 'insideLeft' }}
                      fontSize={12}
                    />
                    <Tooltip 
                      labelFormatter={(value) => `Time: ${formatTimestamp(value as string)}`}
                      formatter={(value: number) => [`${value.toFixed(0)}/min`, 'Throughput']}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="throughput" 
                      stroke="#8b5cf6" 
                      fill="#8b5cf6" 
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        ) : (
          !isLoading && (
            <div className="text-center py-8">
              <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No performance data available for the selected time range.</p>
            </div>
          )
        )}

        {isLoading && (
          <div className="text-center py-8">
            <RefreshCw className="w-8 h-8 text-gray-400 mx-auto mb-4 animate-spin" />
            <p className="text-gray-500">Loading performance metrics...</p>
          </div>
        )}
      </div>
    </div>
  );
}
