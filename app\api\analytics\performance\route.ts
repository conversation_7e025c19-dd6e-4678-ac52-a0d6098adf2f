import { NextRequest, NextResponse } from 'next/server';
import { enhancedDb } from '@/lib/server/enhanced-database';

interface PerformanceData {
  timestamp: string;
  responseTime: number;
  cacheHitRate: number;
  errorRate: number;
  throughput: number;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('range') || '24h';
    
    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (timeRange) {
      case '1h':
        startDate.setHours(startDate.getHours() - 1);
        break;
      case '6h':
        startDate.setHours(startDate.getHours() - 6);
        break;
      case '24h':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      default:
        startDate.setDate(startDate.getDate() - 1);
    }

    // Generate mock performance data for demonstration
    // In a real implementation, this would come from your monitoring system
    const performanceData: PerformanceData[] = [];
    const dataPoints = timeRange === '1h' ? 12 : timeRange === '6h' ? 24 : timeRange === '24h' ? 48 : 168;
    const intervalMs = (endDate.getTime() - startDate.getTime()) / dataPoints;

    for (let i = 0; i < dataPoints; i++) {
      const timestamp = new Date(startDate.getTime() + (i * intervalMs));
      
      // Generate realistic performance metrics with some variation
      const baseResponseTime = 150;
      const baseCacheHitRate = 0.85;
      const baseErrorRate = 0.001;
      const baseThroughput = 45;
      
      // Add some realistic variation
      const timeVariation = Math.sin((i / dataPoints) * Math.PI * 2) * 0.3;
      const randomVariation = (Math.random() - 0.5) * 0.2;
      
      performanceData.push({
        timestamp: timestamp.toISOString(),
        responseTime: Math.max(50, baseResponseTime + (timeVariation + randomVariation) * 100),
        cacheHitRate: Math.min(1, Math.max(0, baseCacheHitRate + (timeVariation + randomVariation) * 0.15)),
        errorRate: Math.max(0, baseErrorRate + Math.abs(randomVariation) * 0.005),
        throughput: Math.max(10, baseThroughput + (timeVariation + randomVariation) * 20),
      });
    }

    // Get real cache statistics from the database
    try {
      const cacheStats = await enhancedDb.getCacheStats();
      
      // Update the latest data point with real cache hit rate if available
      if (performanceData.length > 0 && cacheStats.hitRate !== undefined) {
        performanceData[performanceData.length - 1].cacheHitRate = cacheStats.hitRate;
      }
    } catch (error) {
      console.warn('Could not fetch real cache stats:', error);
    }

    // Get real Gemini service metrics if available
    try {
      const { EnhancedGeminiProfileService } = await import('@/lib/server/enhanced-gemini-service');
      const geminiService = EnhancedGeminiProfileService.getInstance();
      const metrics = geminiService.getMetrics();
      
      // Update the latest data point with real metrics if available
      if (performanceData.length > 0) {
        const latest = performanceData[performanceData.length - 1];
        if (metrics.averageResponseTime > 0) {
          latest.responseTime = metrics.averageResponseTime;
        }
        if (metrics.totalRequests > 0) {
          latest.errorRate = metrics.errorCount / metrics.totalRequests;
        }
      }
    } catch (error) {
      console.warn('Could not fetch Gemini service metrics:', error);
    }

    return NextResponse.json({
      success: true,
      data: performanceData,
      metadata: {
        timeRange,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        dataPoints: performanceData.length,
      },
    });

  } catch (error) {
    console.error('Performance analytics error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch performance data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
