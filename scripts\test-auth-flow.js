const http = require('http');

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = {
            status: res.statusCode,
            headers: res.headers,
            data: body ? JSON.parse(body) : null
          };
          resolve(response);
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: body
          });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testAuthFlow() {
  console.log('🧪 Testing Authentication Flow...\n');

  try {
    // Test 1: Register new user
    console.log('1️⃣ Testing user registration...');
    const registerData = {
      email: `test-${Date.now()}@example.com`,
      name: 'Test User Auth'
    };

    const registerOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const registerResponse = await makeRequest(registerOptions, registerData);
    
    if (registerResponse.status === 200 && registerResponse.data.success) {
      console.log('✅ User registration successful');
      console.log('   User ID:', registerResponse.data.data.user.id);
      console.log('   Email:', registerResponse.data.data.user.email);
    } else {
      console.log('❌ User registration failed');
      console.log('   Status:', registerResponse.status);
      console.log('   Error:', registerResponse.data);
      return;
    }

    // Test 2: Login user
    console.log('\n2️⃣ Testing user login...');
    const loginData = {
      email: registerData.email
    };

    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const loginResponse = await makeRequest(loginOptions, loginData);
    
    if (loginResponse.status === 200 && loginResponse.data.success) {
      console.log('✅ User login successful');
      console.log('   User ID:', loginResponse.data.data.user.id);
      console.log('   Assessments count:', loginResponse.data.data.user.assessments.length);
      
      // Extract session cookie
      const setCookieHeader = loginResponse.headers['set-cookie'];
      let sessionCookie = '';
      if (setCookieHeader) {
        const cookieMatch = setCookieHeader[0].match(/atma_session=([^;]+)/);
        if (cookieMatch) {
          sessionCookie = `atma_session=${cookieMatch[1]}`;
        }
      }
      
      // Test 3: Create assessment with user authentication
      console.log('\n3️⃣ Testing assessment creation with authentication...');
      const assessmentParams = new URLSearchParams({
        r: '25', i: '20', a: '15', s: '18', e: '22', c: '19',
        o: '20', ocean_c: '18', ocean_e: '16', ocean_a: '22', n: '14'
      });

      const assessmentOptions = {
        hostname: 'localhost',
        port: 3000,
        path: `/api/results?${assessmentParams.toString()}`,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': sessionCookie
        },
      };

      console.log('   Creating assessment with user session...');
      const assessmentResponse = await makeRequest(assessmentOptions);
      
      if (assessmentResponse.status === 200 && assessmentResponse.data.success) {
        console.log('✅ Assessment creation with auth successful');
        console.log('   Assessment ID:', assessmentResponse.data.data.assessment.id);
        console.log('   Result ID:', assessmentResponse.data.data.assessment.resultId);
        console.log('   Profile generated:', assessmentResponse.data.data.assessment.profileGenerated);
        
        // Test 4: Verify user profile shows the assessment
        console.log('\n4️⃣ Testing user profile retrieval...');
        const profileOptions = {
          hostname: 'localhost',
          port: 3000,
          path: '/api/auth/profile',
          method: 'GET',
          headers: {
            'Cookie': sessionCookie
          },
        };

        const profileResponse = await makeRequest(profileOptions);
        
        if (profileResponse.status === 200 && profileResponse.data.success) {
          console.log('✅ User profile retrieval successful');
          console.log('   User assessments count:', profileResponse.data.data.user.assessments.length);
          console.log('   Latest assessment ID:', profileResponse.data.data.user.assessments[0]?.id);
        } else {
          console.log('❌ User profile retrieval failed');
          console.log('   Status:', profileResponse.status);
          console.log('   Error:', profileResponse.data);
        }
        
      } else {
        console.log('❌ Assessment creation with auth failed');
        console.log('   Status:', assessmentResponse.status);
        console.log('   Error:', assessmentResponse.data);
      }
      
    } else {
      console.log('❌ User login failed');
      console.log('   Status:', loginResponse.status);
      console.log('   Error:', loginResponse.data);
    }

    // Test 5: Test assessment creation without authentication
    console.log('\n5️⃣ Testing assessment creation without authentication...');
    const noAuthParams = new URLSearchParams({
      r: '20', i: '25', a: '18', s: '15', e: '19', c: '23',
      o: '17', ocean_c: '21', ocean_e: '19', ocean_a: '16', n: '18'
    });

    const noAuthOptions = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/results?${noAuthParams.toString()}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const noAuthResponse = await makeRequest(noAuthOptions);
    
    if (noAuthResponse.status === 200 && noAuthResponse.data.success) {
      console.log('✅ Assessment creation without auth successful');
      console.log('   Assessment ID:', noAuthResponse.data.data.assessment.id);
      console.log('   Result ID:', noAuthResponse.data.data.assessment.resultId);
      console.log('   Profile generated:', noAuthResponse.data.data.assessment.profileGenerated);
      console.log('   Note: This assessment is not linked to any user');
    } else {
      console.log('❌ Assessment creation without auth failed');
      console.log('   Status:', noAuthResponse.status);
      console.log('   Error:', noAuthResponse.data);
    }

    console.log('\n🎉 Authentication flow testing completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testAuthFlow();
