// Authentication Types

export interface User {
  id: string;
  email: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserWithAssessments extends User {
  assessments: AssessmentSummary[];
}

export interface AssessmentSummary {
  id: string;
  resultId: string;
  createdAt: string;
  profileGenerated: boolean;
  profile?: {
    profileTitle: string;
    profileDescription: string;
  };
}

// Authentication Request/Response Types
export interface RegisterRequest {
  email: string;
  name: string;
}

export interface RegisterResponse {
  user: User;
  message: string;
}

export interface LoginRequest {
  email: string;
}

export interface LoginResponse {
  user: UserWithAssessments;
  message: string;
}

export interface UserProfileResponse {
  user: UserWithAssessments;
}

export interface UpdateProfileRequest {
  name?: string;
  email?: string;
}

export interface UpdateProfileResponse {
  user: User;
  message: string;
}

// Authentication Context Types
export interface AuthState {
  user: UserWithAssessments | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface AuthContextType extends AuthState {
  login: (email: string) => Promise<void>;
  register: (email: string, name: string) => Promise<void>;
  logout: () => void;
  updateProfile: (data: UpdateProfileRequest) => Promise<void>;
  refreshUser: () => Promise<void>;
}

// Session Storage Types
export interface UserSession {
  userId: string;
  email: string;
  name: string;
  loginTime: string;
}

// Error Types
export interface AuthError {
  code: string;
  message: string;
}
