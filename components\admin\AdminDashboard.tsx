'use client';

import React, { useState, useEffect } from 'react';
import { 
  Shield, Users, Database, Settings, Activity, 
  AlertTriangle, CheckCircle, Clock, TrendingUp,
  Server, Zap, FileText, BarChart3
} from 'lucide-react';

interface AdminStats {
  users: {
    total: number;
    active: number;
    newToday: number;
    newThisWeek: number;
  };
  system: {
    uptime: number;
    responseTime: number;
    errorRate: number;
    cacheHitRate: number;
  };
  assessments: {
    totalToday: number;
    totalThisWeek: number;
    totalThisMonth: number;
    completionRate: number;
  };
  alerts: {
    critical: number;
    warning: number;
    info: number;
  };
}

interface AdminDashboardProps {
  className?: string;
}

export function AdminDashboard({ className }: AdminDashboardProps) {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState('overview');

  useEffect(() => {
    fetchAdminStats();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchAdminStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchAdminStats = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // In a real implementation, this would fetch from admin API endpoints
      // For now, generate mock admin statistics
      const mockStats = generateMockAdminStats();
      setStats(mockStats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch admin statistics');
    } finally {
      setIsLoading(false);
    }
  };

  const generateMockAdminStats = (): AdminStats => {
    return {
      users: {
        total: 1247,
        active: 89,
        newToday: 12,
        newThisWeek: 67,
      },
      system: {
        uptime: 99.8,
        responseTime: 145,
        errorRate: 0.002,
        cacheHitRate: 0.87,
      },
      assessments: {
        totalToday: 34,
        totalThisWeek: 189,
        totalThisMonth: 756,
        completionRate: 0.92,
      },
      alerts: {
        critical: 0,
        warning: 2,
        info: 5,
      },
    };
  };

  const StatCard = ({ 
    title, 
    value, 
    subtitle, 
    icon: Icon, 
    color,
    trend 
  }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: React.ComponentType<any>;
    color: string;
    trend?: 'up' | 'down' | 'stable';
  }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <div className="flex items-center">
            <p className="text-2xl font-semibold text-gray-900">{value}</p>
            {trend && (
              <div className={`ml-2 flex items-center text-sm ${
                trend === 'up' ? 'text-green-600' : 
                trend === 'down' ? 'text-red-600' : 'text-gray-600'
              }`}>
                <TrendingUp className={`w-4 h-4 ${trend === 'down' ? 'rotate-180' : ''}`} />
              </div>
            )}
          </div>
          {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
        </div>
      </div>
    </div>
  );

  const AlertBadge = ({ type, count }: { type: 'critical' | 'warning' | 'info'; count: number }) => {
    const colors = {
      critical: 'bg-red-100 text-red-800',
      warning: 'bg-yellow-100 text-yellow-800',
      info: 'bg-blue-100 text-blue-800',
    };

    const icons = {
      critical: AlertTriangle,
      warning: AlertTriangle,
      info: CheckCircle,
    };

    const Icon = icons[type];

    return (
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${colors[type]}`}>
        <Icon className="w-4 h-4 mr-1" />
        {count} {type}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center py-12 ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading admin dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-6 ${className}`}>
        <div className="flex items-center">
          <AlertTriangle className="w-6 h-6 text-red-600 mr-3" />
          <span className="text-red-800">Error loading admin dashboard: {error}</span>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className={`text-center py-12 text-gray-500 ${className}`}>
        No admin data available
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Shield className="w-6 h-6 mr-2" />
            Admin Dashboard
          </h2>
          <p className="text-gray-600">System overview and management</p>
        </div>
        <div className="flex items-center space-x-2">
          <Clock className="w-4 h-4 text-gray-500" />
          <span className="text-sm text-gray-600">
            Last updated: {new Date().toLocaleTimeString('id-ID')}
          </span>
        </div>
      </div>

      {/* Alert Summary */}
      {(stats.alerts.critical > 0 || stats.alerts.warning > 0) && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">System Alerts</h3>
          <div className="flex space-x-4">
            {stats.alerts.critical > 0 && <AlertBadge type="critical" count={stats.alerts.critical} />}
            {stats.alerts.warning > 0 && <AlertBadge type="warning" count={stats.alerts.warning} />}
            {stats.alerts.info > 0 && <AlertBadge type="info" count={stats.alerts.info} />}
          </div>
        </div>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Users"
          value={stats.users.total.toLocaleString()}
          subtitle={`${stats.users.active} active now`}
          icon={Users}
          color="bg-blue-500"
          trend="up"
        />
        <StatCard
          title="System Uptime"
          value={`${stats.system.uptime}%`}
          subtitle="Last 30 days"
          icon={Server}
          color="bg-green-500"
          trend="stable"
        />
        <StatCard
          title="Assessments Today"
          value={stats.assessments.totalToday}
          subtitle={`${stats.assessments.totalThisWeek} this week`}
          icon={FileText}
          color="bg-purple-500"
          trend="up"
        />
        <StatCard
          title="Cache Hit Rate"
          value={`${(stats.system.cacheHitRate * 100).toFixed(1)}%`}
          subtitle="Performance metric"
          icon={Zap}
          color="bg-orange-500"
          trend="up"
        />
      </div>

      {/* Detailed Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Statistics */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Users className="w-5 h-5 mr-2" />
            User Statistics
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Registered Users</span>
              <span className="font-semibold">{stats.users.total.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Active Users (24h)</span>
              <span className="font-semibold">{stats.users.active}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">New Users Today</span>
              <span className="font-semibold text-green-600">+{stats.users.newToday}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">New Users This Week</span>
              <span className="font-semibold text-green-600">+{stats.users.newThisWeek}</span>
            </div>
          </div>
        </div>

        {/* System Performance */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            System Performance
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Average Response Time</span>
              <span className="font-semibold">{stats.system.responseTime}ms</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Error Rate</span>
              <span className={`font-semibold ${stats.system.errorRate < 0.01 ? 'text-green-600' : 'text-red-600'}`}>
                {(stats.system.errorRate * 100).toFixed(3)}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Cache Hit Rate</span>
              <span className="font-semibold text-green-600">
                {(stats.system.cacheHitRate * 100).toFixed(1)}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">System Uptime</span>
              <span className="font-semibold text-green-600">{stats.system.uptime}%</span>
            </div>
          </div>
        </div>

        {/* Assessment Statistics */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <BarChart3 className="w-5 h-5 mr-2" />
            Assessment Statistics
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Completed Today</span>
              <span className="font-semibold">{stats.assessments.totalToday}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Completed This Week</span>
              <span className="font-semibold">{stats.assessments.totalThisWeek}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Completed This Month</span>
              <span className="font-semibold">{stats.assessments.totalThisMonth}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Completion Rate</span>
              <span className="font-semibold text-green-600">
                {(stats.assessments.completionRate * 100).toFixed(1)}%
              </span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            Quick Actions
          </h3>
          <div className="space-y-3">
            <button className="w-full text-left px-4 py-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
              <div className="flex items-center">
                <Database className="w-4 h-4 text-blue-600 mr-3" />
                <span className="text-blue-800">Manage Cache</span>
              </div>
            </button>
            <button className="w-full text-left px-4 py-2 bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
              <div className="flex items-center">
                <Users className="w-4 h-4 text-green-600 mr-3" />
                <span className="text-green-800">User Management</span>
              </div>
            </button>
            <button className="w-full text-left px-4 py-2 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
              <div className="flex items-center">
                <Activity className="w-4 h-4 text-purple-600 mr-3" />
                <span className="text-purple-800">System Health</span>
              </div>
            </button>
            <button className="w-full text-left px-4 py-2 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors">
              <div className="flex items-center">
                <Settings className="w-4 h-4 text-orange-600 mr-3" />
                <span className="text-orange-800">System Settings</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
