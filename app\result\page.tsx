"use client";

import { useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback, useRef, Suspense } from 'react';
import Link from 'next/link';
import { RiasecScores, OceanScores } from '@/lib/types';
import { getScoreLevel } from '@/lib/profileStore';
import { resultsApi } from '@/lib/client/apiService';
import { ResultsResponse } from '@/lib/api-types';
import RiasecChart from '@/components/RiasecChart';
import OceanChart from '@/components/OceanChart';
import ProfileSummary from '@/components/ProfileSummary';

function ResultContent() {
  const searchParams = useSearchParams();
  // State untuk data gabungan RIASEC + OCEAN
  const [scores, setScores] = useState<RiasecScores | null>(null);
  const [oceanScores, setOceanScores] = useState<OceanScores | null>(null);
  const [resultsData, setResultsData] = useState<ResultsResponse | null>(null);
  // State umum
  const [error, setError] = useState<string | null>(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState<boolean>(false);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const isGeneratingRef = useRef<boolean>(false);

  // Fungsi untuk generate combined profile interpretation
  const generateCombinedProfileInterpretation = useCallback(async (
    riasecScoresData: RiasecScores,
    oceanScoresData: OceanScores
  ) => {
    // Check if already generating using ref (prevents React Strict Mode double calls)
    if (isGeneratingRef.current || isGenerating) {
      console.log('Combined profile generation already in progress, skipping...');
      return;
    }

    // Additional check to prevent duplicate calls in development mode
    if (resultsData) {
      console.log('Results already exist, skipping duplicate generation...');
      return;
    }

    console.log('Starting combined profile generation for scores:', { riasecScoresData, oceanScoresData });
    isGeneratingRef.current = true;
    setIsGenerating(true);
    setIsLoadingProfile(true);
    try {
      const results = await resultsApi.create(riasecScoresData, oceanScoresData);

      // Redirect to the new result page with unique ID
      window.location.href = `/result/${results.assessment.resultId}`;
    } catch (error) {
      console.error('Error generating combined profile interpretation:', error);
      setError('Gagal menghasilkan interpretasi profil gabungan. Silakan coba lagi.');
      isGeneratingRef.current = false;
      setIsLoadingProfile(false);
      setIsGenerating(false);
    }
  }, [isGenerating, resultsData]);

  useEffect(() => {
    // Reset states saat searchParams berubah
    setError(null);
    setResultsData(null);
    setIsGenerating(false);
    isGeneratingRef.current = false;
    try {
      // Parse RIASEC scores
      const r = parseInt(searchParams.get('r') || '0');
      const i = parseInt(searchParams.get('i') || '0');
      const a = parseInt(searchParams.get('a') || '0');
      const s = parseInt(searchParams.get('s') || '0');
      const e = parseInt(searchParams.get('e') || '0');
      const c = parseInt(searchParams.get('c') || '0');

      // Parse OCEAN scores (required)
      const o = parseInt(searchParams.get('o') || '0');
      const ocean_c = parseInt(searchParams.get('ocean_c') || '0');
      const ocean_e = parseInt(searchParams.get('ocean_e') || '0');
      const ocean_a = parseInt(searchParams.get('ocean_a') || '0');
      const n = parseInt(searchParams.get('n') || '0');

      // Validate RIASEC scores (each should be between 0-30 for 6 questions with 1-5 scale)
      if ([r, i, a, s, e, c].some(score => score < 0 || score > 30 || isNaN(score))) {
        setError('Data hasil tes RIASEC tidak valid. Silakan ulangi tes.');
        return;
      }

      // Validate OCEAN scores (each should be between 5-25 for 5 questions with 1-5 scale)
      if ([o, ocean_c, ocean_e, ocean_a, n].some(score => score < 5 || score > 25 || isNaN(score))) {
        setError('Data hasil tes OCEAN tidak valid. Silakan ulangi tes.');
        return;
      }

      const riasecScoresData = { R: r, I: i, A: a, S: s, E: e, C: c };
      const oceanScoresData = { O: o, C: ocean_c, E: ocean_e, A: ocean_a, N: n };

      // Set both scores
      setScores(riasecScoresData);
      setOceanScores(oceanScoresData);

      // Generate combined profile
      generateCombinedProfileInterpretation(riasecScoresData, oceanScoresData);
    } catch {
      setError('Terjadi kesalahan dalam memproses hasil tes.');
    }
  }, [searchParams]);

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md mx-auto text-center bg-white rounded-xl shadow-sm border border-gray-200 p-8">
          <div className="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Terjadi Kesalahan</h1>
          <p className="text-gray-600 text-sm mb-6">{error}</p>
          <Link 
            href="/"
            className="inline-flex items-center px-4 py-2 bg-gray-900 text-white text-sm font-medium rounded-lg hover:bg-gray-800 transition-colors"
          >
            Kembali ke Beranda
          </Link>
        </div>
      </div>
    );
  }

  if (!scores) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-2 border-gray-300 border-t-gray-900 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 text-sm">Memproses hasil tes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-3">
            Hasil Analisis Kepribadian
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Profil lengkap berdasarkan analisis RIASEC dan Big Five (OCEAN)
          </p>
        </div>

        {/* Profile Summary Section */}
        {isLoadingProfile ? (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8">
            <div className="text-center">
              <div className="w-12 h-12 border-2 border-gray-300 border-t-gray-900 rounded-full animate-spin mx-auto mb-4"></div>
              <h2 className="text-lg font-semibold text-gray-900 mb-2">Menganalisis Profil Anda</h2>
              <p className="text-gray-600 text-sm">
                AI sedang menganalisis skor RIASEC dan OCEAN Anda untuk memberikan interpretasi yang komprehensif.
              </p>
            </div>
          </div>
        ) : resultsData ? (
          <div className="mb-8">
            <ProfileSummary
              combinedProfile={{
                riasecData: {
                  scores: resultsData.assessment.riasecScores,
                  dominantTypes: Object.entries(resultsData.assessment.riasecScores)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 3)
                    .map(([type]) => type as any),
                  level: getScoreLevel(Math.max(...Object.values(resultsData.assessment.riasecScores))).level
                },
                oceanData: {
                  scores: resultsData.assessment.oceanScores,
                  traits: Object.entries(resultsData.assessment.oceanScores).map(([trait, score]) => ({
                    trait: trait as any,
                    name: trait === 'O' ? 'Openness' : trait === 'C' ? 'Conscientiousness' : trait === 'E' ? 'Extraversion' : trait === 'A' ? 'Agreeableness' : 'Neuroticism',
                    score,
                    level: (score > 20 ? 'Tinggi' : score > 15 ? 'Sedang' : 'Rendah') as 'Rendah' | 'Sedang' | 'Tinggi',
                    description: `${trait} level ${score > 20 ? 'tinggi' : score > 15 ? 'sedang' : 'rendah'}`
                  })),
                  personalityType: 'Balanced'
                },
                profileTitle: resultsData.profile.profileTitle,
                profileDescription: resultsData.profile.profileDescription,
                strengths: resultsData.profile.strengths,
                careerSuggestions: resultsData.profile.careerSuggestions,
                workEnvironment: resultsData.profile.workEnvironment,
                developmentAreas: resultsData.profile.developmentAreas,
                personalityInsights: resultsData.profile.personalityInsights,
                careerFit: resultsData.profile.careerFit
              }}
              isCombinedMode={true}
            />
          </div>
        ) : null}

        {/* Charts Section */}
        <div className="space-y-8 mb-12">
          {/* RIASEC Chart */}
          <RiasecChart scores={scores} />
          
          {/* OCEAN Chart */}
          {oceanScores && <OceanChart scores={oceanScores} />}
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Link
            href="/"
            className="inline-flex items-center justify-center px-6 py-3 bg-white text-gray-700 text-sm font-medium rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Kembali ke Beranda
          </Link>

          <Link
            href="/test"
            className="inline-flex items-center justify-center px-6 py-3 bg-gray-900 text-white text-sm font-medium rounded-lg hover:bg-gray-800 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Ulangi Tes
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function ResultPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-2 border-gray-300 border-t-gray-900 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 text-sm">Memuat hasil tes...</p>
        </div>
      </div>
    }>
      <ResultContent />
    </Suspense>
  );
}