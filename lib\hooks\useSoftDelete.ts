import { useState, useCallback } from 'react';
import { 
  UseSoftDeleteOptions, 
  UseSoftDeleteReturn,
  SoftDeleteResponse 
} from '@/lib/utils/soft-delete';

/**
 * Custom hook for handling soft delete operations
 */
export function useSoftDelete(
  baseUrl: string,
  options: UseSoftDeleteOptions = {}
): UseSoftDeleteReturn {
  const [isLoading, setIsLoading] = useState(false);
  const { onSuccess, onError } = options;

  const makeRequest = useCallback(async (
    url: string, 
    method: 'DELETE' | 'PUT',
    action: 'deleted' | 'restored'
  ) => {
    setIsLoading(true);
    
    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to ${action.slice(0, -1)} item`);
      }

      const data: { data: SoftDeleteResponse } = await response.json();
      
      if (data.data.success) {
        onSuccess?.(action);
      } else {
        throw new Error(data.data.message);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      onError?.(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [onSuccess, onError]);

  const softDelete = useCallback(async (id: string) => {
    const url = `${baseUrl}/${id}/soft-delete`;
    await makeRequest(url, 'DELETE', 'deleted');
  }, [baseUrl, makeRequest]);

  const restore = useCallback(async (id: string) => {
    const url = `${baseUrl}/${id}/soft-delete`;
    await makeRequest(url, 'PUT', 'restored');
  }, [baseUrl, makeRequest]);

  const permanentDelete = useCallback(async (id: string) => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`${baseUrl}/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to permanently delete item');
      }

      onSuccess?.('deleted');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      onError?.(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [baseUrl, onSuccess, onError]);

  return {
    isLoading,
    softDelete,
    restore,
    permanentDelete,
  };
}

/**
 * Hook for bulk soft delete operations
 */
export interface UseBulkSoftDeleteOptions {
  onSuccess?: (action: 'deleted' | 'restored', count: number) => void;
  onError?: (error: string) => void;
  onProgress?: (processed: number, total: number) => void;
}

export interface UseBulkSoftDeleteReturn {
  isLoading: boolean;
  progress: number;
  bulkSoftDelete: (ids: string[]) => Promise<void>;
  bulkRestore: (ids: string[]) => Promise<void>;
  bulkPermanentDelete: (ids: string[]) => Promise<void>;
}

export function useBulkSoftDelete(
  baseUrl: string,
  options: UseBulkSoftDeleteOptions = {}
): UseBulkSoftDeleteReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const { onSuccess, onError, onProgress } = options;

  const processBulkOperation = useCallback(async (
    ids: string[],
    operation: (id: string) => Promise<void>,
    action: 'deleted' | 'restored'
  ) => {
    setIsLoading(true);
    setProgress(0);
    
    let processed = 0;
    const errors: string[] = [];

    try {
      for (const id of ids) {
        try {
          await operation(id);
          processed++;
        } catch (error) {
          errors.push(`Failed to process ${id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        
        const currentProgress = Math.round((processed / ids.length) * 100);
        setProgress(currentProgress);
        onProgress?.(processed, ids.length);
      }

      if (errors.length > 0) {
        onError?.(`Some operations failed: ${errors.join(', ')}`);
      } else {
        onSuccess?.(action, processed);
      }
    } catch (error) {
      onError?.(error instanceof Error ? error.message : 'Bulk operation failed');
    } finally {
      setIsLoading(false);
      setProgress(0);
    }
  }, [onSuccess, onError, onProgress]);

  const singleSoftDelete = useSoftDelete(baseUrl);

  const bulkSoftDelete = useCallback(async (ids: string[]) => {
    await processBulkOperation(ids, singleSoftDelete.softDelete, 'deleted');
  }, [processBulkOperation, singleSoftDelete.softDelete]);

  const bulkRestore = useCallback(async (ids: string[]) => {
    await processBulkOperation(ids, singleSoftDelete.restore, 'restored');
  }, [processBulkOperation, singleSoftDelete.restore]);

  const bulkPermanentDelete = useCallback(async (ids: string[]) => {
    await processBulkOperation(ids, singleSoftDelete.permanentDelete, 'deleted');
  }, [processBulkOperation, singleSoftDelete.permanentDelete]);

  return {
    isLoading,
    progress,
    bulkSoftDelete,
    bulkRestore,
    bulkPermanentDelete,
  };
}

/**
 * Hook for soft delete status checking
 */
export interface SoftDeleteStatus {
  id: string;
  isDeleted: boolean;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface UseSoftDeleteStatusReturn {
  status: SoftDeleteStatus | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useSoftDeleteStatus(
  baseUrl: string,
  id: string
): UseSoftDeleteStatusReturn {
  const [status, setStatus] = useState<SoftDeleteStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = useCallback(async () => {
    if (!id) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${baseUrl}/${id}/soft-delete`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch soft delete status');
      }
      
      const data = await response.json();
      setStatus(data.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [baseUrl, id]);

  // Auto-fetch on mount and when id changes
  useState(() => {
    fetchStatus();
  });

  return {
    status,
    isLoading,
    error,
    refetch: fetchStatus,
  };
}

/**
 * Hook for soft delete confirmation dialogs
 */
export interface UseSoftDeleteConfirmationOptions {
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
}

export interface UseSoftDeleteConfirmationReturn {
  showConfirmation: (options?: UseSoftDeleteConfirmationOptions) => Promise<boolean>;
  isOpen: boolean;
  close: () => void;
}

export function useSoftDeleteConfirmation(): UseSoftDeleteConfirmationReturn {
  const [isOpen, setIsOpen] = useState(false);
  const [resolvePromise, setResolvePromise] = useState<((value: boolean) => void) | null>(null);

  const showConfirmation = useCallback((options: UseSoftDeleteConfirmationOptions = {}) => {
    return new Promise<boolean>((resolve) => {
      setIsOpen(true);
      setResolvePromise(() => resolve);
    });
  }, []);

  const close = useCallback(() => {
    setIsOpen(false);
    if (resolvePromise) {
      resolvePromise(false);
      setResolvePromise(null);
    }
  }, [resolvePromise]);

  const confirm = useCallback(() => {
    setIsOpen(false);
    if (resolvePromise) {
      resolvePromise(true);
      setResolvePromise(null);
    }
  }, [resolvePromise]);

  return {
    showConfirmation,
    isOpen,
    close,
  };
}
