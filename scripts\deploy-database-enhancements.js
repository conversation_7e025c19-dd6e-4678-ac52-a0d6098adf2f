#!/usr/bin/env node

/**
 * Master Deployment Script for Database Enhancements
 * 
 * This script orchestrates the complete deployment of database enhancements
 * including schema changes, data validation, and feature testing.
 */

const { exec } = require('child_process');
const { promisify } = require('util');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const execAsync = promisify(exec);

// Import our custom scripts
const { enhanceDatabaseSchema } = require('./database-enhancement-migration.js');
const { addDataValidationConstraints } = require('./add-data-validation.js');
const { testDatabaseEnhancements } = require('./test-database-enhancements.js');

class DatabaseEnhancementDeployer {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  async deploy() {
    try {
      console.log('🚀 Database Enhancement Deployment');
      console.log('=====================================');
      console.log('This will deploy comprehensive database enhancements including:');
      console.log('  ✓ Performance indexes and optimization');
      console.log('  ✓ Soft delete functionality');
      console.log('  ✓ Data validation constraints');
      console.log('  ✓ Audit trail system');
      console.log('  ✓ AI caching layer');
      console.log('  ✓ Normalized data storage');
      console.log('  ✓ Analytics and versioning');
      console.log('  ✓ Advanced features');
      console.log('');

      // Pre-deployment checks
      await this.preDeploymentChecks();

      // Confirm deployment
      const confirmed = await this.confirmDeployment();
      if (!confirmed) {
        console.log('❌ Deployment cancelled by user');
        return;
      }

      // Execute deployment phases
      await this.executeDeployment();

      // Post-deployment validation
      await this.postDeploymentValidation();

      console.log('\n🎉 Database Enhancement Deployment Completed Successfully!');
      console.log('\n📋 Next Steps:');
      console.log('  1. Update your application code to use EnhancedDatabaseService');
      console.log('  2. Implement soft delete logic in your UI');
      console.log('  3. Set up monitoring for the new analytics tables');
      console.log('  4. Configure AI cache cleanup schedules');
      console.log('  5. Review the documentation in docs/DATABASE_ENHANCEMENTS.md');

    } catch (error) {
      console.error('\n💥 Deployment Failed:', error.message);
      console.log('\n🔧 Recovery Options:');
      console.log('  1. Check backup files in scripts/ directory');
      console.log('  2. Review error logs above');
      console.log('  3. Run individual migration steps manually');
      console.log('  4. Contact support if issues persist');
      throw error;
    } finally {
      this.rl.close();
    }
  }

  async preDeploymentChecks() {
    console.log('🔍 Pre-deployment Checks');
    console.log('========================');

    // Check Node.js version
    const nodeVersion = process.version;
    console.log(`✅ Node.js version: ${nodeVersion}`);

    // Check if Prisma is available
    try {
      await execAsync('npx prisma --version');
      console.log('✅ Prisma CLI available');
    } catch (error) {
      throw new Error('Prisma CLI not found. Please install: npm install prisma @prisma/client');
    }

    // Check database connection
    try {
      const { PrismaClient } = require('../lib/generated/prisma');
      const prisma = new PrismaClient();
      await prisma.$queryRaw`SELECT 1`;
      await prisma.$disconnect();
      console.log('✅ Database connection successful');
    } catch (error) {
      throw new Error('Database connection failed. Check your DATABASE_URL in .env file');
    }

    // Check if schema file exists
    const schemaPath = path.join(__dirname, '../prisma/schema.prisma');
    if (!fs.existsSync(schemaPath)) {
      throw new Error('Prisma schema file not found');
    }
    console.log('✅ Prisma schema file found');

    // Check available disk space (basic check)
    try {
      const stats = fs.statSync(__dirname);
      console.log('✅ File system accessible');
    } catch (error) {
      throw new Error('File system access error');
    }

    // Check if backup directory exists, create if not
    const backupDir = path.join(__dirname);
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    console.log('✅ Backup directory ready');

    console.log('✅ All pre-deployment checks passed\n');
  }

  async confirmDeployment() {
    return new Promise((resolve) => {
      this.rl.question('⚠️  This will modify your database schema. Continue? (y/N): ', (answer) => {
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });
  }

  async executeDeployment() {
    console.log('🔧 Executing Deployment Phases');
    console.log('==============================');

    // Phase 1: Prisma Schema Migration
    console.log('\n📋 Phase 1: Prisma Schema Migration');
    console.log('-----------------------------------');
    try {
      console.log('Generating Prisma client...');
      await execAsync('npx prisma generate');
      console.log('✅ Prisma client generated');

      console.log('Creating database migration...');
      const migrationName = `enhancement_${Date.now()}`;
      await execAsync(`npx prisma migrate dev --name ${migrationName}`);
      console.log('✅ Database migration applied');
    } catch (error) {
      console.error('❌ Prisma migration failed:', error.message);
      throw error;
    }

    // Phase 2: Custom Schema Enhancements
    console.log('\n📋 Phase 2: Custom Schema Enhancements');
    console.log('--------------------------------------');
    try {
      await enhanceDatabaseSchema();
      console.log('✅ Custom schema enhancements applied');
    } catch (error) {
      console.error('❌ Custom enhancements failed:', error.message);
      throw error;
    }

    // Phase 3: Data Validation Constraints
    console.log('\n📋 Phase 3: Data Validation Constraints');
    console.log('---------------------------------------');
    try {
      await addDataValidationConstraints();
      console.log('✅ Data validation constraints added');
    } catch (error) {
      console.error('❌ Validation constraints failed:', error.message);
      throw error;
    }

    // Phase 4: Final Schema Sync
    console.log('\n📋 Phase 4: Final Schema Synchronization');
    console.log('----------------------------------------');
    try {
      await execAsync('npx prisma db push');
      console.log('✅ Schema synchronized');

      await execAsync('npx prisma generate');
      console.log('✅ Final Prisma client generated');
    } catch (error) {
      console.error('❌ Final sync failed:', error.message);
      throw error;
    }
  }

  async postDeploymentValidation() {
    console.log('\n🧪 Post-deployment Validation');
    console.log('=============================');

    try {
      await testDatabaseEnhancements();
      console.log('✅ All validation tests passed');
    } catch (error) {
      console.error('❌ Validation tests failed:', error.message);
      throw error;
    }

    // Additional checks
    console.log('\n🔍 Additional Verification');
    console.log('--------------------------');

    try {
      const { PrismaClient } = require('../lib/generated/prisma');
      const prisma = new PrismaClient();

      // Check table counts
      const userCount = await prisma.user.count();
      const assessmentCount = await prisma.assessment.count();
      const profileCount = await prisma.profile.count();

      console.log(`✅ Database integrity verified:`);
      console.log(`   - Users: ${userCount}`);
      console.log(`   - Assessments: ${assessmentCount}`);
      console.log(`   - Profiles: ${profileCount}`);

      // Check new tables exist
      const tables = await prisma.$queryRaw`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name IN (
          'assessment_history', 'ai_cache', 'profile_strengths',
          'career_suggestions', 'development_areas', 'assessment_analytics',
          'user_sessions', 'profile_versions', 'profile_comparisons',
          'career_recommendations'
        )
        ORDER BY name
      `;

      console.log(`✅ New tables verified: ${tables.length}/10 tables created`);

      await prisma.$disconnect();
    } catch (error) {
      console.error('❌ Additional verification failed:', error.message);
      throw error;
    }
  }

  async generateDeploymentReport() {
    const timestamp = new Date().toISOString();
    const report = {
      timestamp,
      deployment: 'Database Enhancements',
      version: '1.0.0',
      status: 'SUCCESS',
      phases: [
        'Prisma Schema Migration',
        'Custom Schema Enhancements',
        'Data Validation Constraints',
        'Final Schema Synchronization',
        'Post-deployment Validation'
      ],
      features: [
        'Performance Indexes',
        'Soft Delete System',
        'Data Validation Constraints',
        'Audit Trail System',
        'AI Caching Layer',
        'Normalized Data Storage',
        'Analytics System',
        'Profile Versioning',
        'Advanced Features'
      ]
    };

    const reportPath = path.join(__dirname, `deployment-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`📄 Deployment report saved: ${reportPath}`);
  }
}

// Main execution
async function main() {
  const deployer = new DatabaseEnhancementDeployer();
  
  try {
    await deployer.deploy();
    await deployer.generateDeploymentReport();
    
    console.log('\n🎊 Deployment completed successfully!');
    console.log('Your database is now enhanced with advanced features.');
    
  } catch (error) {
    console.error('\n💥 Deployment failed:', error.message);
    console.log('\nFor support, please check:');
    console.log('  - Backup files in scripts/ directory');
    console.log('  - Error logs above');
    console.log('  - Documentation in docs/DATABASE_ENHANCEMENTS.md');
    
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { DatabaseEnhancementDeployer };
