import { CombinedProfileInterpretation } from '@/lib/profileStore';

interface ProfileSummaryProps {
  combinedProfile?: CombinedProfileInterpretation | null;
  isCombinedMode?: boolean;
}

export default function ProfileSummary({
  combinedProfile
}: ProfileSummaryProps) {
  const data = combinedProfile;

  if (!data) return null;

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-8">
      {/* Header Section */}
      <div className="px-6 py-8 border-b border-gray-200">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-gray-900 mb-3">
            {data.profileTitle}
          </h1>
          <p className="text-gray-600 text-base leading-relaxed max-w-2xl mx-auto">
            {data.profileDescription}
          </p>
        </div>
      </div>

      {/* Content Grid */}
      <div className="p-6 space-y-8">
        {/* Strengths & Career Suggestions */}
        <div className="grid md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <div className="w-1 h-4 bg-gray-900 mr-3 rounded-full"></div>
              Kekuatan Utama
            </h2>
            <ul className="space-y-3">
              {data.strengths.map((strength: string, index: number) => (
                <li key={index} className="flex items-start text-gray-700 group">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0 group-hover:bg-gray-600 transition-colors"></div>
                  <span className="text-sm leading-relaxed group-hover:text-gray-900 transition-colors">{strength}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <div className="w-1 h-4 bg-gray-900 mr-3 rounded-full"></div>
              Rekomendasi Karir
            </h2>
            <ul className="space-y-3">
              {data.careerSuggestions.slice(0, 5).map((career: string, index: number) => (
                <li key={index} className="flex items-start text-gray-700 group">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0 group-hover:bg-gray-600 transition-colors"></div>
                  <span className="text-sm leading-relaxed group-hover:text-gray-900 transition-colors">{career}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Personality & Development */}
        <div className="grid md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <div className="w-1 h-4 bg-gray-900 mr-3 rounded-full"></div>
              Wawasan Kepribadian
            </h2>
            <ul className="space-y-3">
              {data.personalityInsights.map((insight: string, index: number) => (
                <li key={index} className="flex items-start text-gray-700 group">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0 group-hover:bg-gray-600 transition-colors"></div>
                  <span className="text-sm leading-relaxed group-hover:text-gray-900 transition-colors">{insight}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <div className="w-1 h-4 bg-gray-900 mr-3 rounded-full"></div>
              Area Pengembangan
            </h2>
            <ul className="space-y-3">
              {data.developmentAreas.map((area: string, index: number) => (
                <li key={index} className="flex items-start text-gray-700 group">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0 group-hover:bg-gray-600 transition-colors"></div>
                  <span className="text-sm leading-relaxed group-hover:text-gray-900 transition-colors">{area}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Work Environment & Career Fit */}
        <div className="grid md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <div className="w-1 h-4 bg-gray-900 mr-3 rounded-full"></div>
              Lingkungan Kerja Ideal
            </h2>
            <p className="text-gray-700 text-sm leading-relaxed">
              {data.workEnvironment}
            </p>
          </div>

          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <div className="w-1 h-4 bg-gray-900 mr-3 rounded-full"></div>
              Kesesuaian Karir
            </h2>
            <p className="text-gray-700 text-sm leading-relaxed">
              {data.careerFit}
            </p>
          </div>
        </div>

        {/* Important Note */}
        <div className="pt-6 border-t border-gray-200">
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
            <p className="text-gray-600 text-sm leading-relaxed">
              <span className="font-medium">Catatan Penting:</span> Hasil ini menggambarkan minat dan preferensi Anda, bukan kemampuan atau keahlian.
              Gunakan sebagai panduan untuk mengeksplorasi bidang yang menarik bagi Anda dan pertimbangkan untuk mengembangkan keterampilan yang diperlukan.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}