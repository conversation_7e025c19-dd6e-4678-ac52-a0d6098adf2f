'use client';

import React, { useState } from 'react';
import { AnalyticsDashboard } from '@/components/analytics/AnalyticsDashboard';
import { CacheManagement } from '@/components/analytics/CacheManagement';
import { RiasecTrendChart } from '@/components/charts/RiasecTrendChart';
import { OceanDistributionChart } from '@/components/charts/OceanDistributionChart';
import { 
  BarChart3, TrendingUp, Users, Database, 
  Activity, Download, Eye, EyeOff 
} from 'lucide-react';

export default function AnalyticsPage() {
  const [showAdvancedCharts, setShowAdvancedCharts] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <BarChart3 className="w-8 h-8 mr-3 text-blue-600" />
                Analytics Dashboard
              </h1>
              <p className="mt-2 text-gray-600">
                Comprehensive system monitoring and analytics for ATMA
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowAdvancedCharts(!showAdvancedCharts)}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  showAdvancedCharts 
                    ? 'bg-blue-100 text-blue-700 hover:bg-blue-200' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {showAdvancedCharts ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
                {showAdvancedCharts ? 'Hide' : 'Show'} Advanced Charts
              </button>
              <a
                href="/admin"
                className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
              >
                <Activity className="w-4 h-4 mr-2" />
                Admin Panel
              </a>
            </div>
          </div>
        </div>
        
        <div className="space-y-8">
          {/* Main Analytics Dashboard with Tabs */}
          <AnalyticsDashboard />
          
          {/* Advanced Charts Section */}
          {showAdvancedCharts && (
            <div className="space-y-8">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Advanced Analytics Charts
                </h2>
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                  <RiasecTrendChart timeRange="30d" />
                  <OceanDistributionChart chartType="bar" />
                </div>
              </div>
              
              {/* Additional Chart Variations */}
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                <RiasecTrendChart timeRange="7d" />
                <OceanDistributionChart chartType="pie" />
              </div>
            </div>
          )}
          
          {/* Cache Management */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <Database className="w-5 h-5 mr-2" />
              System Management
            </h2>
            <CacheManagement />
          </div>
          
          {/* Quick Stats Summary */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow p-6 text-white">
            <h2 className="text-xl font-semibold mb-4">Phase 3 Implementation Status</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold">✅</div>
                <div className="text-sm opacity-90 mt-2">Enhanced Analytics</div>
                <div className="text-xs opacity-75">Performance, User Behavior, System Health</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">✅</div>
                <div className="text-sm opacity-90 mt-2">Advanced Charts</div>
                <div className="text-xs opacity-75">RIASEC Trends, OCEAN Distribution</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">✅</div>
                <div className="text-sm opacity-90 mt-2">Admin Interface</div>
                <div className="text-xs opacity-75">System Management & Monitoring</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
