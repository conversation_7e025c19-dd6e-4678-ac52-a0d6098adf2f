'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { 
  AuthState, 
  AuthContextType, 
  UserWithAssessments, 
  UpdateProfileRequest,
  UserSession 
} from '@/lib/auth-types';

// Action types untuk reducer
type AuthAction =
  | { type: 'SET_LOADING'; loading: boolean }
  | { type: 'SET_USER'; user: UserWithAssessments | null }
  | { type: 'UPDATE_USER'; user: UserWithAssessments }
  | { type: 'LOGOUT' };

// Initial state
const initialState: AuthState = {
  user: null,
  isLoading: true,
  isAuthenticated: false,
};

// Reducer function
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.loading,
      };
    
    case 'SET_USER':
      return {
        ...state,
        user: action.user,
        isAuthenticated: !!action.user,
        isLoading: false,
      };
    
    case 'UPDATE_USER':
      return {
        ...state,
        user: action.user,
      };
    
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
      };
    
    default:
      return state;
  }
}

// Local storage keys
const SESSION_KEY = 'atma_user_session';

// Helper functions for session management
const saveSession = (user: UserWithAssessments) => {
  const session: UserSession = {
    userId: user.id,
    email: user.email,
    name: user.name,
    loginTime: new Date().toISOString(),
  };
  localStorage.setItem(SESSION_KEY, JSON.stringify(session));
};

const getSession = (): UserSession | null => {
  try {
    const sessionData = localStorage.getItem(SESSION_KEY);
    return sessionData ? JSON.parse(sessionData) : null;
  } catch {
    return null;
  }
};

const clearSession = () => {
  localStorage.removeItem(SESSION_KEY);
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      const session = getSession();
      if (session) {
        try {
          // Verify session with server and get updated user data
          const response = await fetch('/api/auth/profile');
          if (response.ok) {
            const data = await response.json();
            dispatch({ type: 'SET_USER', user: data.user });
          } else {
            // Session invalid, clear it
            clearSession();
            dispatch({ type: 'SET_USER', user: null });
          }
        } catch (error) {
          console.error('Failed to verify session:', error);
          clearSession();
          dispatch({ type: 'SET_USER', user: null });
        }
      } else {
        dispatch({ type: 'SET_USER', user: null });
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (email: string) => {
    dispatch({ type: 'SET_LOADING', loading: true });
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Login failed');
      }

      const data = await response.json();
      saveSession(data.user);
      dispatch({ type: 'SET_USER', user: data.user });
    } catch (error) {
      dispatch({ type: 'SET_LOADING', loading: false });
      throw error;
    }
  };

  // Register function
  const register = async (email: string, name: string) => {
    dispatch({ type: 'SET_LOADING', loading: true });
    
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, name }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Registration failed');
      }

      const data = await response.json();
      // After registration, automatically log in
      await login(email);
    } catch (error) {
      dispatch({ type: 'SET_LOADING', loading: false });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Call logout API to clear server-side session
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Logout API error:', error);
    }

    clearSession();
    dispatch({ type: 'LOGOUT' });
  };

  // Update profile function
  const updateProfile = async (data: UpdateProfileRequest) => {
    try {
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Profile update failed');
      }

      const result = await response.json();
      
      // Update local session
      if (state.user) {
        const updatedUser = { ...state.user, ...result.user };
        saveSession(updatedUser);
        dispatch({ type: 'UPDATE_USER', user: updatedUser });
      }
    } catch (error) {
      throw error;
    }
  };

  // Refresh user data
  const refreshUser = async () => {
    if (!state.user) return;

    try {
      const response = await fetch('/api/auth/profile');
      if (response.ok) {
        const data = await response.json();
        saveSession(data.user);
        dispatch({ type: 'UPDATE_USER', user: data.user });
      }
    } catch (error) {
      console.error('Failed to refresh user data:', error);
    }
  };

  const contextValue: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    updateProfile,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook untuk menggunakan context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
