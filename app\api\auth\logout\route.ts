import { NextRequest } from 'next/server';
import { createSuccessResponse } from '@/lib/api-utils';
import { withMiddleware } from '@/lib/server/middleware';

async function postHandler(req: NextRequest) {
  const response = createSuccessResponse({
    message: 'Logout successful',
  });

  // Clear session cookie
  response.cookies.set('atma_session', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 0, // Expire immediately
    path: '/',
  });

  return response;
}

export const POST = withMiddleware(postHandler);
