'use client';

import React, { useState, useEffect } from 'react';
import {
  Database,
  Trash2,
  RefreshCw,
  TrendingUp,
  Clock,
  Zap,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react';

interface CacheStats {
  totalEntries: number;
  totalHits: number;
  hitRate: number;
  oldestEntry: string | null;
  newestEntry: string | null;
}

interface CacheManagementProps {
  className?: string;
}

export function CacheManagement({ className = '' }: CacheManagementProps) {
  const [stats, setStats] = useState<CacheStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isClearing, setIsClearing] = useState(false);
  const [isCleaning, setIsCleaning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastAction, setLastAction] = useState<string | null>(null);

  useEffect(() => {
    fetchCacheStats();
  }, []);

  const fetchCacheStats = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/analytics');
      
      if (!response.ok) {
        throw new Error('Failed to fetch cache statistics');
      }

      const data = await response.json();
      setStats(data.data.cacheStats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const clearCache = async () => {
    if (!confirm('Are you sure you want to clear all cache entries? This action cannot be undone.')) {
      return;
    }

    setIsClearing(true);
    setError(null);

    try {
      const response = await fetch('/api/cache/clear', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to clear cache');
      }

      const result = await response.json();
      setLastAction(`Cleared ${result.data.deletedCount} cache entries`);
      await fetchCacheStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear cache');
    } finally {
      setIsClearing(false);
    }
  };

  const cleanExpiredCache = async () => {
    setIsCleaning(true);
    setError(null);

    try {
      const response = await fetch('/api/cache/cleanup', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to clean expired cache');
      }

      const result = await response.json();
      setLastAction(`Cleaned ${result.data.deletedCount} expired cache entries`);
      await fetchCacheStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clean expired cache');
    } finally {
      setIsCleaning(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Intl.DateTimeFormat('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(dateString));
  };

  const getHitRateColor = (hitRate: number) => {
    if (hitRate >= 80) return 'text-green-600';
    if (hitRate >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHitRateStatus = (hitRate: number) => {
    if (hitRate >= 80) return 'Excellent';
    if (hitRate >= 60) return 'Good';
    if (hitRate >= 40) return 'Fair';
    return 'Poor';
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center py-8 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading cache statistics...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center">
          <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
          <span className="text-red-800">Error: {error}</span>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className={`text-center py-8 text-gray-500 ${className}`}>
        No cache statistics available
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Database className="w-5 h-5 mr-2" />
            Cache Management
          </h3>
          <button
            onClick={fetchCacheStats}
            disabled={isLoading}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            title="Refresh statistics"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      <div className="p-6">
        {/* Last Action Alert */}
        {lastAction && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
              <span className="text-green-800">{lastAction}</span>
            </div>
          </div>
        )}

        {/* Cache Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.totalEntries.toLocaleString()}</div>
            <div className="text-sm text-gray-600">Total Entries</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.totalHits.toLocaleString()}</div>
            <div className="text-sm text-gray-600">Total Hits</div>
          </div>
          
          <div className="text-center">
            <div className={`text-2xl font-bold ${getHitRateColor(stats.hitRate)}`}>
              {stats.hitRate.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">
              Hit Rate ({getHitRateStatus(stats.hitRate)})
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {stats.totalEntries > 0 ? Math.round(stats.totalHits / stats.totalEntries * 100) / 100 : 0}
            </div>
            <div className="text-sm text-gray-600">Avg Hits/Entry</div>
          </div>
        </div>

        {/* Cache Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              Cache Timeline
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Oldest Entry:</span>
                <span className="font-medium">{formatDate(stats.oldestEntry)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Newest Entry:</span>
                <span className="font-medium">{formatDate(stats.newestEntry)}</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <TrendingUp className="w-4 h-4 mr-2" />
              Performance Impact
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Cache Efficiency:</span>
                <span className={`font-medium ${getHitRateColor(stats.hitRate)}`}>
                  {getHitRateStatus(stats.hitRate)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Estimated Savings:</span>
                <span className="font-medium text-green-600">
                  {Math.round(stats.hitRate * stats.totalHits / 100)} requests
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Cache Actions */}
        <div className="border-t border-gray-200 pt-6">
          <h4 className="font-medium text-gray-900 mb-4">Cache Actions</h4>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={cleanExpiredCache}
              disabled={isCleaning}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isCleaning ? (
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
              ) : (
                <Zap className="w-4 h-4 mr-2" />
              )}
              {isCleaning ? 'Cleaning...' : 'Clean Expired'}
            </button>

            <button
              onClick={clearCache}
              disabled={isClearing}
              className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isClearing ? (
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
              ) : (
                <Trash2 className="w-4 h-4 mr-2" />
              )}
              {isClearing ? 'Clearing...' : 'Clear All Cache'}
            </button>

            <button
              onClick={fetchCacheStats}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh Stats
            </button>
          </div>
        </div>

        {/* Cache Recommendations */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Recommendations</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            {stats.hitRate < 60 && (
              <li>• Consider increasing cache TTL to improve hit rate</li>
            )}
            {stats.totalEntries > 5000 && (
              <li>• Large cache size detected - consider periodic cleanup</li>
            )}
            {stats.hitRate > 90 && (
              <li>• Excellent cache performance! Current settings are optimal</li>
            )}
            <li>• Run cleanup regularly to remove expired entries</li>
            <li>• Monitor hit rate trends to optimize cache strategy</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
