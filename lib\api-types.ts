// API Request/Response Types
import { RiasecScores, OceanScores } from './types';

// Assessment API Types
export interface AssessmentRequest {
  riasecScores: RiasecScores;
  oceanScores: OceanScores;
  userId?: string; // Optional for future user management
}

export interface AssessmentResponse {
  id: string;
  resultId: string;
  riasecScores: RiasecScores;
  oceanScores: OceanScores;
  createdAt: string;
  profileGenerated: boolean;
}

// Profile Generation API Types
export interface ProfileGenerationRequest {
  riasecScores: RiasecScores;
  oceanScores: OceanScores;
}

export interface ProfileGenerationResponse {
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  workEnvironment: string;
  developmentAreas: string[];
  personalityInsights: string[];
  careerFit: string;
  generatedAt: string;
}

// Results API Types
export interface ResultsRequest {
  assessmentId: string;
}

export interface ResultsResponse {
  assessment: AssessmentResponse;
  profile: ProfileGenerationResponse;
}

// Assessment History API Types
export interface AssessmentHistoryItem {
  id: string;
  riasecScores: RiasecScores;
  oceanScores: OceanScores;
  createdAt: string;
  profileGenerated: boolean;
  profileTitle?: string;
}

export interface AssessmentHistoryResponse {
  assessments: AssessmentHistoryItem[];
  pagination: {
    limit: number;
    offset: number;
    total: number;
    hasMore: boolean;
  };
}

// Assessment with Profile API Types
export interface AssessmentWithProfileResponse {
  assessment: {
    id: string;
    riasecScores: RiasecScores;
    oceanScores: OceanScores;
    createdAt: string;
    profileGenerated: boolean;
  };
  profile?: {
    profileTitle: string;
    profileDescription: string;
    strengths: string[];
    careerSuggestions: string[];
    workEnvironment: string;
    developmentAreas: string[];
    personalityInsights: string[];
    careerFit: string;
    generatedAt: string;
  };
}

// Error Response Type
export interface ApiErrorResponse {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

// Success Response Wrapper
export interface ApiSuccessResponse<T> {
  success: true;
  data: T;
  timestamp: string;
}

// Generic API Response
export type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;
