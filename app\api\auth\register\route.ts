import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
} from '@/lib/api-utils';
import { RegisterRequest, RegisterResponse } from '@/lib/auth-types';
import { db } from '@/lib/server/database';
import { withMiddleware, sanitizeInput } from '@/lib/server/middleware';

async function postHandler(req: NextRequest) {
  const body: RegisterRequest = await req.json();

  // Validate required fields
  if (!body.email || !body.name) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Email and name are required',
      400
    );
  }

  // Sanitize inputs
  const email = sanitizeInput(body.email.toLowerCase().trim());
  const name = sanitizeInput(body.name.trim());

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid email format',
      400
    );
  }

  // Validate name length
  if (name.length < 2 || name.length > 50) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Name must be between 2 and 50 characters',
      400
    );
  }

  try {
    // Check if user already exists
    const existingUser = await db.userExists(email);
    if (existingUser) {
      return createErrorResponse(
        'USER_EXISTS',
        'User with this email already exists',
        409
      );
    }

    // Create new user
    const user = await db.createUser(email, name);

    const response: RegisterResponse = {
      user: {
        id: user.id,
        email: user.email!,
        name: user.name!,
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
      },
      message: 'User registered successfully',
    };

    return createSuccessResponse(response);
  } catch (error) {
    console.error('Registration error:', error);
    
    if (error instanceof Error && error.message.includes('already exists')) {
      return createErrorResponse(
        'USER_EXISTS',
        'User with this email already exists',
        409
      );
    }

    return createErrorResponse(
      'REGISTRATION_ERROR',
      'Failed to register user',
      500
    );
  }
}

export const POST = withMiddleware(postHandler);
