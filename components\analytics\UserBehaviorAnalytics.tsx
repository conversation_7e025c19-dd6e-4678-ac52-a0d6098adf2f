'use client';

import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, Pie, BarChart, Bar, Cell, XAxis, YAxis, 
  CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer 
} from 'recharts';
import { Users, Clock, Smartphone, Monitor, RefreshCw, TrendingUp } from 'lucide-react';

interface UserBehaviorData {
  sessionDuration: {
    average: number;
    distribution: { range: string; count: number }[];
  };
  assessmentCompletionRate: number;
  profileViewRate: number;
  returnUserRate: number;
  deviceTypes: { name: string; value: number; color: string }[];
  timeOfDayUsage: { hour: number; users: number }[];
  userJourney: {
    step: string;
    users: number;
    dropoffRate: number;
  }[];
}

interface UserBehaviorAnalyticsProps {
  className?: string;
}

export function UserBehaviorAnalytics({ className }: UserBehaviorAnalyticsProps) {
  const [data, setData] = useState<UserBehaviorData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('7d');

  const fetchUserBehaviorData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/analytics/users?range=${timeRange}`);
      if (!response.ok) {
        throw new Error('Failed to fetch user behavior data');
      }

      const result = await response.json();
      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch user behavior data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUserBehaviorData();
  }, [timeRange]);

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes.toFixed(0)}m`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins.toFixed(0)}m`;
  };

  const formatHour = (hour: number) => {
    return `${hour.toString().padStart(2, '0')}:00`;
  };

  const DEVICE_COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Users className="w-5 h-5 mr-2" />
            User Behavior Analytics
          </h3>
          <div className="flex items-center space-x-4">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
            <button
              onClick={fetchUserBehaviorData}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh data"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="p-6">
        {data ? (
          <div className="space-y-8">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600">Avg Session Duration</p>
                    <p className="text-2xl font-bold text-blue-900">{formatDuration(data.sessionDuration.average)}</p>
                  </div>
                  <Clock className="w-8 h-8 text-blue-500" />
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600">Completion Rate</p>
                    <p className="text-2xl font-bold text-green-900">{(data.assessmentCompletionRate * 100).toFixed(1)}%</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-green-500" />
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600">Profile View Rate</p>
                    <p className="text-2xl font-bold text-purple-900">{(data.profileViewRate * 100).toFixed(1)}%</p>
                  </div>
                  <Users className="w-8 h-8 text-purple-500" />
                </div>
              </div>

              <div className="bg-orange-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600">Return User Rate</p>
                    <p className="text-2xl font-bold text-orange-900">{(data.returnUserRate * 100).toFixed(1)}%</p>
                  </div>
                  <RefreshCw className="w-8 h-8 text-orange-500" />
                </div>
              </div>
            </div>

            {/* Charts Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Device Types */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-4">Device Types</h4>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={data.deviceTypes}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {data.deviceTypes.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={DEVICE_COLORS[index % DEVICE_COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value: number) => [`${value} users`, 'Count']} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Session Duration Distribution */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-4">Session Duration Distribution</h4>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={data.sessionDuration.distribution}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="range" fontSize={12} />
                      <YAxis fontSize={12} />
                      <Tooltip formatter={(value: number) => [`${value} sessions`, 'Count']} />
                      <Bar dataKey="count" fill="#3b82f6" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* Time of Day Usage */}
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-4">Usage by Time of Day</h4>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={data.timeOfDayUsage}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="hour" 
                      tickFormatter={formatHour}
                      fontSize={12}
                    />
                    <YAxis 
                      label={{ value: 'Active Users', angle: -90, position: 'insideLeft' }}
                      fontSize={12}
                    />
                    <Tooltip 
                      labelFormatter={(hour) => `Time: ${formatHour(hour as number)}`}
                      formatter={(value: number) => [`${value} users`, 'Active Users']}
                    />
                    <Bar dataKey="users" fill="#10b981" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* User Journey Funnel */}
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-4">User Journey Funnel</h4>
              <div className="space-y-2">
                {data.userJourney.map((step, index) => {
                  const maxUsers = Math.max(...data.userJourney.map(s => s.users));
                  const widthPercentage = (step.users / maxUsers) * 100;
                  
                  return (
                    <div key={step.step} className="relative">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-700">{step.step}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-600">{step.users} users</span>
                          {index > 0 && (
                            <span className="text-xs text-red-600">
                              -{(step.dropoffRate * 100).toFixed(1)}% dropoff
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-8">
                        <div 
                          className="bg-blue-600 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                          style={{ width: `${widthPercentage}%` }}
                        >
                          {widthPercentage > 20 && `${step.users}`}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        ) : (
          !isLoading && (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No user behavior data available for the selected time range.</p>
            </div>
          )
        )}

        {isLoading && (
          <div className="text-center py-8">
            <RefreshCw className="w-8 h-8 text-gray-400 mx-auto mb-4 animate-spin" />
            <p className="text-gray-500">Loading user behavior analytics...</p>
          </div>
        )}
      </div>
    </div>
  );
}
