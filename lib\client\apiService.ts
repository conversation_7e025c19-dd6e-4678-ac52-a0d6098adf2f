import {
  AssessmentRequest,
  AssessmentResponse,
  ProfileGenerationRequest,
  ProfileGenerationResponse,
  ResultsResponse,
  AssessmentHistoryResponse,
  AssessmentWithProfileResponse
} from '@/lib/api-types';
import { RiasecScores, OceanScores } from '@/lib/types';

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

// Generic API call function
async function apiCall<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}/api${endpoint}`;
  
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  
  // Check if response has success field (our API format)
  if ('success' in data && data.success) {
    return data.data;
  }
  
  // If no success field, return data directly
  return data;
}

// Assessment API calls
export const assessmentApi = {
  // Create new assessment
  create: async (riasecScores: RiasecScores, oceanScores: OceanScores): Promise<AssessmentResponse> => {
    const request: AssessmentRequest = {
      riasecScores,
      oceanScores,
    };

    return apiCall<AssessmentResponse>('/assessments', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },

  // Get assessment by ID
  get: async (assessmentId: string): Promise<AssessmentResponse> => {
    return apiCall<AssessmentResponse>(`/assessments?id=${assessmentId}`);
  },

  // Get assessment history
  getHistory: async (limit: number = 20, offset: number = 0): Promise<AssessmentHistoryResponse> => {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });
    return apiCall<AssessmentHistoryResponse>(`/assessments/history?${params.toString()}`);
  },

  // Get assessment with profile by ID
  getWithProfile: async (assessmentId: string): Promise<AssessmentWithProfileResponse> => {
    return apiCall<AssessmentWithProfileResponse>(`/assessments/${assessmentId}`);
  },
};

// Profile generation API calls
export const profileApi = {
  // Generate profile from scores
  generate: async (riasecScores: RiasecScores, oceanScores: OceanScores): Promise<ProfileGenerationResponse> => {
    const request: ProfileGenerationRequest = {
      riasecScores,
      oceanScores,
    };

    return apiCall<ProfileGenerationResponse>('/profiles/generate', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },
};

// Results API calls
export const resultsApi = {
  // Create new results with profile generation
  create: async (riasecScores: RiasecScores, oceanScores: OceanScores): Promise<ResultsResponse> => {
    const params = new URLSearchParams({
      // RIASEC scores
      r: riasecScores.R.toString(),
      i: riasecScores.I.toString(),
      a: riasecScores.A.toString(),
      s: riasecScores.S.toString(),
      e: riasecScores.E.toString(),
      c: riasecScores.C.toString(),
      // OCEAN scores
      o: oceanScores.O.toString(),
      ocean_c: oceanScores.C.toString(),
      ocean_e: oceanScores.E.toString(),
      ocean_a: oceanScores.A.toString(),
      n: oceanScores.N.toString(),
    });

    return apiCall<ResultsResponse>(`/results?${params.toString()}`, {
      method: 'POST',
    });
  },

  // Get existing results by resultId
  get: async (resultId: string): Promise<ResultsResponse> => {
    return apiCall<ResultsResponse>(`/results?resultId=${resultId}`);
  },
};

// Health check
export const healthApi = {
  check: async (): Promise<{ status: string; message: string; timestamp: string }> => {
    return apiCall<{ status: string; message: string; timestamp: string }>('/health');
  },
};

// Error handling utility
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public errorCode?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Wrapper function with error handling
export async function safeApiCall<T>(
  apiFunction: () => Promise<T>,
  fallbackValue?: T
): Promise<T | null> {
  try {
    return await apiFunction();
  } catch (error) {
    console.error('API call failed:', error);
    
    if (fallbackValue !== undefined) {
      return fallbackValue;
    }
    
    return null;
  }
}
