import { NextRequest, NextResponse } from 'next/server';
import { enhancedDb } from '@/lib/server/enhanced-database';

interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  dateRange: {
    start: string;
    end: string;
  };
  dataTypes: {
    analytics: boolean;
    performance: boolean;
    userBehavior: boolean;
    cacheStats: boolean;
    systemHealth: boolean;
  };
  includeCharts: boolean;
  includeRawData: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const options: ExportOptions = await request.json();
    
    // Validate options
    if (!options.format || !options.dateRange.start || !options.dateRange.end) {
      return NextResponse.json(
        { success: false, error: 'Invalid export options' },
        { status: 400 }
      );
    }

    // Check if any data types are selected
    const selectedDataTypes = Object.values(options.dataTypes).some(Boolean);
    if (!selectedDataTypes) {
      return NextResponse.json(
        { success: false, error: 'No data types selected for export' },
        { status: 400 }
      );
    }

    // Collect data based on selected types
    const exportData: any = {};
    
    if (options.dataTypes.analytics) {
      try {
        const analyticsData = await enhancedDb.getAnalyticsOverview();
        exportData.analytics = analyticsData;
      } catch (error) {
        console.warn('Could not fetch analytics data:', error);
      }
    }

    if (options.dataTypes.performance) {
      try {
        // Fetch performance data from the performance endpoint
        const performanceResponse = await fetch(`${request.nextUrl.origin}/api/analytics/performance?range=7d`);
        if (performanceResponse.ok) {
          const performanceData = await performanceResponse.json();
          exportData.performance = performanceData.data;
        }
      } catch (error) {
        console.warn('Could not fetch performance data:', error);
      }
    }

    if (options.dataTypes.userBehavior) {
      try {
        // Fetch user behavior data from the users endpoint
        const userResponse = await fetch(`${request.nextUrl.origin}/api/analytics/users?range=7d`);
        if (userResponse.ok) {
          const userData = await userResponse.json();
          exportData.userBehavior = userData.data;
        }
      } catch (error) {
        console.warn('Could not fetch user behavior data:', error);
      }
    }

    if (options.dataTypes.cacheStats) {
      try {
        const cacheStats = await enhancedDb.getCacheStats();
        exportData.cacheStats = cacheStats;
      } catch (error) {
        console.warn('Could not fetch cache stats:', error);
      }
    }

    if (options.dataTypes.systemHealth) {
      // System health data would come from monitoring system
      exportData.systemHealth = {
        note: 'System health data export not yet implemented',
        timestamp: new Date().toISOString(),
      };
    }

    // Generate export based on format
    switch (options.format) {
      case 'csv':
        return generateCSVExport(exportData, options);
      case 'excel':
        return generateExcelExport(exportData, options);
      case 'pdf':
        return generatePDFExport(exportData, options);
      default:
        return NextResponse.json(
          { success: false, error: 'Unsupported export format' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Export error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Export failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

function generateCSVExport(data: any, options: ExportOptions) {
  let csvContent = '';
  
  // Add header
  csvContent += `ATMA Analytics Export\n`;
  csvContent += `Generated: ${new Date().toISOString()}\n`;
  csvContent += `Date Range: ${options.dateRange.start} to ${options.dateRange.end}\n\n`;

  // Add analytics overview if available
  if (data.analytics) {
    csvContent += `Analytics Overview\n`;
    csvContent += `Total Assessments,${data.analytics.totalAssessments}\n`;
    csvContent += `Total Profiles,${data.analytics.totalProfiles}\n`;
    csvContent += `Total Users,${data.analytics.totalUsers}\n`;
    csvContent += `Cache Hit Rate,${(data.analytics.cacheHitRate * 100).toFixed(2)}%\n\n`;
  }

  // Add performance data if available
  if (data.performance && Array.isArray(data.performance)) {
    csvContent += `Performance Data\n`;
    csvContent += `Timestamp,Response Time (ms),Cache Hit Rate,Error Rate,Throughput\n`;
    data.performance.forEach((row: any) => {
      csvContent += `${row.timestamp},${row.responseTime},${row.cacheHitRate},${row.errorRate},${row.throughput}\n`;
    });
    csvContent += `\n`;
  }

  // Add cache stats if available
  if (data.cacheStats) {
    csvContent += `Cache Statistics\n`;
    csvContent += `Total Entries,${data.cacheStats.totalEntries}\n`;
    csvContent += `Total Hits,${data.cacheStats.totalHits}\n`;
    csvContent += `Hit Rate,${(data.cacheStats.hitRate * 100).toFixed(2)}%\n`;
    csvContent += `Oldest Entry,${data.cacheStats.oldestEntry || 'N/A'}\n`;
    csvContent += `Newest Entry,${data.cacheStats.newestEntry || 'N/A'}\n\n`;
  }

  const blob = new Blob([csvContent], { type: 'text/csv' });
  
  return new NextResponse(blob, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="analytics-export-${new Date().toISOString().split('T')[0]}.csv"`,
    },
  });
}

function generateExcelExport(data: any, options: ExportOptions) {
  // For now, return CSV format with Excel MIME type
  // In a real implementation, you would use a library like xlsx to generate proper Excel files
  let content = '';
  
  content += `ATMA Analytics Export\n`;
  content += `Generated: ${new Date().toISOString()}\n`;
  content += `Date Range: ${options.dateRange.start} to ${options.dateRange.end}\n\n`;

  if (data.analytics) {
    content += `Analytics Overview\n`;
    content += `Metric,Value\n`;
    content += `Total Assessments,${data.analytics.totalAssessments}\n`;
    content += `Total Profiles,${data.analytics.totalProfiles}\n`;
    content += `Total Users,${data.analytics.totalUsers}\n`;
    content += `Cache Hit Rate,${(data.analytics.cacheHitRate * 100).toFixed(2)}%\n\n`;
  }

  const blob = new Blob([content], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  });
  
  return new NextResponse(blob, {
    headers: {
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="analytics-export-${new Date().toISOString().split('T')[0]}.xlsx"`,
    },
  });
}

function generatePDFExport(data: any, options: ExportOptions) {
  // For now, return a simple text-based PDF
  // In a real implementation, you would use a library like jsPDF or Puppeteer
  let content = `
ATMA Analytics Export Report

Generated: ${new Date().toLocaleString()}
Date Range: ${options.dateRange.start} to ${options.dateRange.end}

`;

  if (data.analytics) {
    content += `
Analytics Overview:
- Total Assessments: ${data.analytics.totalAssessments}
- Total Profiles: ${data.analytics.totalProfiles}
- Total Users: ${data.analytics.totalUsers}
- Cache Hit Rate: ${(data.analytics.cacheHitRate * 100).toFixed(2)}%

`;
  }

  if (data.cacheStats) {
    content += `
Cache Statistics:
- Total Entries: ${data.cacheStats.totalEntries}
- Total Hits: ${data.cacheStats.totalHits}
- Hit Rate: ${(data.cacheStats.hitRate * 100).toFixed(2)}%

`;
  }

  content += `
Note: This is a simplified PDF export. 
Full PDF generation with charts and formatting requires additional implementation.
`;

  const blob = new Blob([content], { type: 'application/pdf' });
  
  return new NextResponse(blob, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="analytics-export-${new Date().toISOString().split('T')[0]}.pdf"`,
    },
  });
}
