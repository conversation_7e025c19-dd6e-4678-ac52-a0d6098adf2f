import Link from 'next/link';
import { AssessmentHistoryItem } from '@/lib/api-types';

export default function ProfileHistoryCard({ assessment }: { assessment: AssessmentHistoryItem }) {
  // Fungsi yang sama seperti sebelumnya...
  const getTopRiasec = () => {
    const types = [
      { type: 'R', score: assessment.riasecScores.R, name: 'Realistic' },
      { type: 'I', score: assessment.riasecScores.I, name: 'Investigative' },
      { type: 'A', score: assessment.riasecScores.A, name: 'Artistic' },
      { type: 'S', score: assessment.riasecScores.S, name: 'Social' },
      { type: 'E', score: assessment.riasecScores.E, name: 'Enterprising' },
      { type: 'C', score: assessment.riasecScores.C, name: 'Conventional' },
    ];
    return types.sort((a, b) => b.score - a.score).slice(0, 2);
  };

  const getTopOcean = () => {
    const traits = [
      { name: 'Openness', score: assessment.oceanScores.O },
      { name: 'Conscientiousness', score: assessment.oceanScores.C },
      { name: 'Extraversion', score: assessment.oceanScores.E },
      { name: 'Agreeableness', score: assessment.oceanScores.A },
      { name: 'Neuroticism', score: assessment.oceanScores.N },
    ];
    return traits.sort((a, b) => b.score - a.score).slice(0, 2);
  };

  const getTimeAgo = (dateString: string) => {
    const hours = Math.floor((Date.now() - new Date(dateString).getTime()) / (1000 * 60 * 60));
    
    if (hours < 1) return 'Baru saja';
    if (hours < 24) return `${hours} jam yang lalu`;
    
    const days = Math.floor(hours / 24);
    if (days < 7) return `${days} hari yang lalu`;
    if (days < 30) return `${Math.floor(days / 7)} minggu yang lalu`;
    
    return `${Math.floor(days / 30)} bulan yang lalu`;
  };

  const title = assessment.profileGenerated && assessment.profileTitle 
    ? assessment.profileTitle 
    : 'Hasil Tes Kepribadian';

  const isComplete = assessment.profileGenerated;
  const topRiasec = getTopRiasec();
  const topOcean = getTopOcean();

  return (
    <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden h-full flex flex-col border border-gray-200 max-w-md mx-auto">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 bg-gray-50 flex-shrink-0">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="text-xl font-semibold text-gray-900 mb-2 truncate">
              {title}
            </h3>
            <p className="text-sm text-gray-500">
              {getTimeAgo(assessment.createdAt)}
            </p>
          </div>
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
            isComplete 
              ? 'bg-gray-900 text-white' 
              : 'bg-gray-100 text-gray-600'
          }`}>
            {isComplete ? 'Lengkap' : 'Menunggu'}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6 flex-grow flex flex-col">
        {/* RIASEC Section */}
        <div>
          <h4 className="text-base font-medium text-gray-900 mb-4 flex items-center">
            <div className="w-1 h-4 bg-gray-900 mr-3 rounded-full"></div>
            Minat Karir Utama
          </h4>
          <div className="flex flex-wrap gap-2">
            {topRiasec.map((type, i) => (
              <span 
                key={type.type}
                className={`px-3 py-2 rounded-lg text-sm font-medium ${
                  i === 0 
                    ? 'bg-gray-100 text-gray-800 ring-1 ring-gray-300' 
                    : 'bg-gray-50 text-gray-600'
                }`}
              >
                {type.name} ({type.score})
              </span>
            ))}
          </div>
        </div>

        {/* OCEAN Section */}
        <div>
          <h4 className="text-base font-medium text-gray-900 mb-4 flex items-center">
            <div className="w-1 h-4 bg-gray-900 mr-3 rounded-full"></div>
            Kepribadian Dominan
          </h4>
          <div className="flex flex-wrap gap-2">
            {topOcean.map((trait, i) => (
              <span 
                key={trait.name}
                className={`px-3 py-2 rounded-lg text-sm font-medium ${
                  i === 0 
                    ? 'bg-gray-100 text-gray-800 ring-1 ring-gray-300' 
                    : 'bg-gray-50 text-gray-600'
                }`}
              >
                {trait.name} ({trait.score})
              </span>
            ))}
          </div>
        </div>

        {/* Action Button */}
        <div className="pt-4 mt-auto">
          <Link 
            href={`/profile/${assessment.id}`}
            className="group flex items-center justify-center gap-2 w-full bg-gray-900 text-white py-3 px-4 rounded-lg hover:bg-gray-800 transition-all duration-200 text-sm font-medium shadow-sm"
          >
            {isComplete ? 'Lihat Detail' : 'Lihat Hasil'}
            <svg className="w-4 h-4 transform group-hover:translate-x-0.5 transition-transform" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M5 12h14M12 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      </div>
    </div>
  );
}