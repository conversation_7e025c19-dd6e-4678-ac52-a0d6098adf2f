'use client';

import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON>A<PERSON>s, CartesianGrid, 
  <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell 
} from 'recharts';
import { <PERSON><PERSON><PERSON>3, Pie<PERSON><PERSON> as Pie<PERSON><PERSON>I<PERSON>, Users, TrendingUp } from 'lucide-react';

interface OceanDistributionData {
  dimension: string;
  fullName: string;
  low: number;
  medium: number;
  high: number;
  average: number;
  totalUsers: number;
}

interface OceanDistributionChartProps {
  className?: string;
  chartType?: 'bar' | 'pie';
}

export function OceanDistributionChart({ className, chartType = 'bar' }: OceanDistributionChartProps) {
  const [data, setData] = useState<OceanDistributionData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDimension, setSelectedDimension] = useState<string>('O');
  const [viewType, setViewType] = useState<'distribution' | 'averages'>(chartType === 'pie' ? 'distribution' : 'averages');

  useEffect(() => {
    fetchDistributionData();
  }, []);

  const fetchDistributionData = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would fetch from an API
      // For now, generate mock distribution data
      const mockData = generateMockDistributionData();
      setData(mockData);
    } catch (error) {
      console.error('Failed to fetch OCEAN distribution data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateMockDistributionData = (): OceanDistributionData[] => {
    const dimensions = [
      { key: 'O', name: 'Openness' },
      { key: 'C', name: 'Conscientiousness' },
      { key: 'E', name: 'Extraversion' },
      { key: 'A', name: 'Agreeableness' },
      { key: 'N', name: 'Neuroticism' },
    ];

    return dimensions.map(dim => {
      // Generate realistic distribution
      const totalUsers = Math.floor(Math.random() * 500) + 200;
      const low = Math.floor(totalUsers * (0.2 + Math.random() * 0.1)); // 20-30%
      const high = Math.floor(totalUsers * (0.2 + Math.random() * 0.1)); // 20-30%
      const medium = totalUsers - low - high; // Remaining
      const average = 2.5 + (Math.random() - 0.5) * 1; // 2.0 - 3.0

      return {
        dimension: dim.key,
        fullName: dim.name,
        low,
        medium,
        high,
        average,
        totalUsers,
      };
    });
  };

  const oceanColors = {
    O: '#3b82f6', // Blue
    C: '#10b981', // Green
    E: '#f59e0b', // Orange
    A: '#8b5cf6', // Purple
    N: '#ef4444', // Red
  };

  const distributionColors = {
    low: '#fca5a5',    // Light red
    medium: '#fbbf24', // Yellow
    high: '#34d399',   // Green
  };

  const getSelectedDimensionData = () => {
    return data.find(d => d.dimension === selectedDimension);
  };

  const getPieData = () => {
    const selected = getSelectedDimensionData();
    if (!selected) return [];

    return [
      { name: 'Low (1-2)', value: selected.low, color: distributionColors.low },
      { name: 'Medium (2-4)', value: selected.medium, color: distributionColors.medium },
      { name: 'High (4-5)', value: selected.high, color: distributionColors.high },
    ];
  };

  const formatTooltip = (value: number, name: string) => {
    if (viewType === 'distribution') {
      return [`${value} users`, name];
    } else {
      return [`${value.toFixed(2)}`, name];
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Users className="w-5 h-5 mr-2" />
            OCEAN Personality Distribution
          </h3>
          <div className="flex items-center space-x-4">
            <select
              value={viewType}
              onChange={(e) => setViewType(e.target.value as 'distribution' | 'averages')}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="distribution">Score Distribution</option>
              <option value="averages">Average Scores</option>
            </select>
            <div className="flex border border-gray-300 rounded-md">
              <button
                onClick={() => setViewType('averages')}
                className={`p-2 ${viewType === 'averages' ? 'bg-blue-500 text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                title="Bar Chart"
              >
                <BarChart3 className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewType('distribution')}
                className={`p-2 ${viewType === 'distribution' ? 'bg-blue-500 text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                title="Pie Chart"
              >
                <PieChartIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading distribution data...</span>
          </div>
        ) : (
          <div className="space-y-6">
            {viewType === 'distribution' && (
              <div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Dimension:
                  </label>
                  <div className="flex space-x-2">
                    {data.map((item) => (
                      <button
                        key={item.dimension}
                        onClick={() => setSelectedDimension(item.dimension)}
                        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                          selectedDimension === item.dimension
                            ? 'text-white'
                            : 'text-gray-600 bg-gray-100 hover:bg-gray-200'
                        }`}
                        style={{
                          backgroundColor: selectedDimension === item.dimension 
                            ? oceanColors[item.dimension as keyof typeof oceanColors] 
                            : undefined
                        }}
                      >
                        {item.fullName}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Pie Chart */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">
                      {getSelectedDimensionData()?.fullName} Distribution
                    </h4>
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={getPieData()}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {getPieData().map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip formatter={(value: number) => [`${value} users`, 'Count']} />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </div>

                  {/* Statistics */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">Statistics</h4>
                    <div className="space-y-4">
                      {getSelectedDimensionData() && (
                        <>
                          <div className="bg-gray-50 p-4 rounded-lg">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600">Total Users:</span>
                              <span className="font-semibold">{getSelectedDimensionData()!.totalUsers}</span>
                            </div>
                          </div>
                          <div className="bg-gray-50 p-4 rounded-lg">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600">Average Score:</span>
                              <span className="font-semibold">{getSelectedDimensionData()!.average.toFixed(2)}</span>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Low Scores:</span>
                              <span className="font-medium">{getSelectedDimensionData()!.low} users</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Medium Scores:</span>
                              <span className="font-medium">{getSelectedDimensionData()!.medium} users</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">High Scores:</span>
                              <span className="font-medium">{getSelectedDimensionData()!.high} users</span>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {viewType === 'averages' && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Average Scores by Dimension</h4>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={data}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="fullName" 
                        fontSize={12}
                        angle={-45}
                        textAnchor="end"
                        height={80}
                      />
                      <YAxis 
                        domain={[1, 5]}
                        label={{ value: 'Average Score', angle: -90, position: 'insideLeft' }}
                        fontSize={12}
                      />
                      <Tooltip formatter={formatTooltip} />
                      <Bar 
                        dataKey="average" 
                        fill="#3b82f6"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            )}

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {data.map((item) => (
                <div 
                  key={item.dimension}
                  className="p-4 rounded-lg border-2"
                  style={{
                    borderColor: oceanColors[item.dimension as keyof typeof oceanColors],
                    backgroundColor: `${oceanColors[item.dimension as keyof typeof oceanColors]}10`,
                  }}
                >
                  <div className="text-center">
                    <p className="text-xs font-medium text-gray-600">{item.fullName}</p>
                    <p 
                      className="text-2xl font-bold"
                      style={{ color: oceanColors[item.dimension as keyof typeof oceanColors] }}
                    >
                      {item.average.toFixed(1)}
                    </p>
                    <p className="text-xs text-gray-500">{item.totalUsers} users</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
