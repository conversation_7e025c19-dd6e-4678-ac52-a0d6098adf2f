'use client';

import Link from 'next/link';
import { useAuth } from '@/store/authStore';

export default function Home() {
  const { isAuthenticated, user } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-lg mx-auto text-center bg-white rounded-lg shadow-sm p-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">
            AI-Driven Talent Mapping Assessment
          </h1>
          <p className="text-gray-600">
            Temukan minat karir berdasarkan kepribadian Anda
          </p>
        </div>

        {/* Info */}
        <div className="bg-gray-50 rounded-lg p-4 mb-8 text-left">
          <div className="text-sm text-gray-600 space-y-1">
            <div>• 55 pertanyaan, ~20 menit</div>
            <div>• Profil minat karir personal</div>
            <div>• Visual<PERSON>si hasil</div>
            {isAuthenticated && (
              <div>• Hasil tersimpan di akun <PERSON>a</div>
            )}
          </div>
        </div>

        {/* User Status */}
        {isAuthenticated ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <p className="text-sm font-medium text-green-800">
                  Selamat datang kembali, {user?.name}!
                </p>
                <p className="text-xs text-green-600">
                  Hasil tes akan disimpan di akun Anda
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <p className="text-sm font-medium text-blue-800">
                  Anda dapat menggunakan aplikasi tanpa login
                </p>
                <p className="text-xs text-blue-600">
                  Namun hasil tidak akan disimpan. <Link href="/login" className="underline">Masuk</Link> untuk menyimpan riwayat.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Buttons */}
        <div className="space-y-3">
          <Link
            href="/test"
            className="block w-full bg-gray-800 hover:bg-gray-900 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Mulai Tes
          </Link>

          {isAuthenticated && (
            <Link
              href="/dashboard"
              className="block w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              Dashboard Saya
            </Link>
          )}

          <Link
            href="/demo-visualization"
            className="block w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Demo Visualisasi
          </Link>
        </div>

        {/* Note */}
        <p className="text-xs text-gray-500 mt-6">
          Jawab dengan jujur untuk hasil akurat
        </p>
      </div>
    </div>
  );
}