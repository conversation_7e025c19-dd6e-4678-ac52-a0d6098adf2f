import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
} from '@/lib/api-utils';
import { 
  UserProfileResponse, 
  UpdateProfileRequest, 
  UpdateProfileResponse,
  UserWithAssessments 
} from '@/lib/auth-types';
import { db } from '@/lib/server/database';
import { withMiddleware, sanitizeInput } from '@/lib/server/middleware';

// Helper function to get user ID from session
// Simple session implementation using cookies
async function getUserIdFromSession(req: NextRequest): Promise<string | null> {
  const sessionCookie = req.cookies.get('atma_session');
  if (!sessionCookie) return null;

  try {
    // In a real app, you'd validate and decrypt the session
    // For now, we'll just parse the JSON (not secure for production)
    const session = JSON.parse(sessionCookie.value);
    return session.userId || null;
  } catch {
    return null;
  }
}

// GET - Get user profile
async function getHandler(req: NextRequest) {
  const userId = await getUserIdFromSession(req);
  
  if (!userId) {
    return createErrorResponse(
      'UNAUTHORIZED',
      'User not authenticated',
      401
    );
  }

  try {
    const dbUser = await db.getUserById(userId);
    
    if (!dbUser) {
      return createErrorResponse(
        'USER_NOT_FOUND',
        'User not found',
        404
      );
    }

    // Transform assessments data
    const assessments = dbUser.assessments.map(assessment => ({
      id: assessment.id,
      resultId: assessment.resultId || assessment.id,
      createdAt: assessment.createdAt.toISOString(),
      profileGenerated: !!assessment.profile,
      profile: assessment.profile ? {
        profileTitle: assessment.profile.profileTitle,
        profileDescription: assessment.profile.profileDescription,
      } : undefined,
    }));

    const user: UserWithAssessments = {
      id: dbUser.id,
      email: dbUser.email!,
      name: dbUser.name!,
      createdAt: dbUser.createdAt.toISOString(),
      updatedAt: dbUser.updatedAt.toISOString(),
      assessments,
    };

    const response: UserProfileResponse = { user };
    return createSuccessResponse(response);
  } catch (error) {
    console.error('Get profile error:', error);
    
    return createErrorResponse(
      'PROFILE_ERROR',
      'Failed to get user profile',
      500
    );
  }
}

// PUT - Update user profile
async function putHandler(req: NextRequest) {
  const userId = await getUserIdFromSession(req);
  
  if (!userId) {
    return createErrorResponse(
      'UNAUTHORIZED',
      'User not authenticated',
      401
    );
  }

  const body: UpdateProfileRequest = await req.json();

  // Validate and sanitize inputs
  const updateData: { email?: string; name?: string } = {};

  if (body.email !== undefined) {
    const email = sanitizeInput(body.email.toLowerCase().trim());
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!emailRegex.test(email)) {
      return createErrorResponse(
        'VALIDATION_ERROR',
        'Invalid email format',
        400
      );
    }
    
    updateData.email = email;
  }

  if (body.name !== undefined) {
    const name = sanitizeInput(body.name.trim());
    
    if (name.length < 2 || name.length > 50) {
      return createErrorResponse(
        'VALIDATION_ERROR',
        'Name must be between 2 and 50 characters',
        400
      );
    }
    
    updateData.name = name;
  }

  if (Object.keys(updateData).length === 0) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'No valid fields to update',
      400
    );
  }

  try {
    const updatedUser = await db.updateUser(userId, updateData);

    const response: UpdateProfileResponse = {
      user: {
        id: updatedUser.id,
        email: updatedUser.email!,
        name: updatedUser.name!,
        createdAt: updatedUser.createdAt.toISOString(),
        updatedAt: updatedUser.updatedAt.toISOString(),
      },
      message: 'Profile updated successfully',
    };

    return createSuccessResponse(response);
  } catch (error) {
    console.error('Update profile error:', error);
    
    if (error instanceof Error && error.message.includes('already taken')) {
      return createErrorResponse(
        'EMAIL_TAKEN',
        'Email is already taken by another user',
        409
      );
    }

    return createErrorResponse(
      'PROFILE_ERROR',
      'Failed to update user profile',
      500
    );
  }
}

export const GET = withMiddleware(getHandler);
export const PUT = withMiddleware(putHandler);
