'use client';

import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  User, 
  Activity, 
  Filter, 
  ChevronDown, 
  ChevronUp,
  Eye,
  EyeOff,
  Calendar,
  MapPin,
  Monitor
} from 'lucide-react';

interface AuditEntry {
  id: string;
  action: string;
  oldValues: any;
  newValues: any;
  userId: string | null;
  timestamp: string;
  ipAddress: string | null;
  userAgent: string | null;
}

interface AuditTrailProps {
  assessmentId: string;
  className?: string;
}

export function AuditTrail({ assessmentId, className = '' }: AuditTrailProps) {
  const [entries, setEntries] = useState<AuditEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalEntries, setTotalEntries] = useState(0);
  const [filters, setFilters] = useState({
    action: '',
    userId: '',
    startDate: '',
    endDate: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [expandedEntries, setExpandedEntries] = useState<Set<string>>(new Set());

  const pageSize = 20;

  useEffect(() => {
    fetchAuditTrail();
  }, [assessmentId, page, filters]);

  const fetchAuditTrail = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        ),
      });

      const response = await fetch(`/api/assessments/${assessmentId}/audit?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch audit trail');
      }

      const data = await response.json();
      setEntries(data.data.entries);
      setTotalEntries(data.data.totalEntries);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1); // Reset to first page when filtering
  };

  const toggleEntryExpansion = (entryId: string) => {
    setExpandedEntries(prev => {
      const newSet = new Set(prev);
      if (newSet.has(entryId)) {
        newSet.delete(entryId);
      } else {
        newSet.add(entryId);
      }
      return newSet;
    });
  };

  const getActionIcon = (action: string) => {
    switch (action.toLowerCase()) {
      case 'created':
        return <Activity className="w-4 h-4 text-green-600" />;
      case 'updated':
        return <Activity className="w-4 h-4 text-blue-600" />;
      case 'deleted':
        return <Activity className="w-4 h-4 text-red-600" />;
      case 'restored':
        return <Activity className="w-4 h-4 text-green-600" />;
      case 'profile_generated':
        return <Activity className="w-4 h-4 text-purple-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action.toLowerCase()) {
      case 'created':
        return 'bg-green-100 text-green-800';
      case 'updated':
        return 'bg-blue-100 text-blue-800';
      case 'deleted':
        return 'bg-red-100 text-red-800';
      case 'restored':
        return 'bg-green-100 text-green-800';
      case 'profile_generated':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Intl.DateTimeFormat('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }).format(new Date(timestamp));
  };

  const renderFilters = () => (
    <div className={`bg-gray-50 p-4 rounded-lg mb-4 transition-all duration-200 ${showFilters ? 'block' : 'hidden'}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Action
          </label>
          <select
            value={filters.action}
            onChange={(e) => handleFilterChange('action', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Actions</option>
            <option value="created">Created</option>
            <option value="updated">Updated</option>
            <option value="deleted">Deleted</option>
            <option value="restored">Restored</option>
            <option value="profile_generated">Profile Generated</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            User ID
          </label>
          <input
            type="text"
            value={filters.userId}
            onChange={(e) => handleFilterChange('userId', e.target.value)}
            placeholder="Enter user ID"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Start Date
          </label>
          <input
            type="datetime-local"
            value={filters.startDate}
            onChange={(e) => handleFilterChange('startDate', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            End Date
          </label>
          <input
            type="datetime-local"
            value={filters.endDate}
            onChange={(e) => handleFilterChange('endDate', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  );

  const renderEntry = (entry: AuditEntry) => {
    const isExpanded = expandedEntries.has(entry.id);
    const hasDetails = entry.oldValues || entry.newValues;

    return (
      <div key={entry.id} className="bg-white border border-gray-200 rounded-lg p-4 mb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            {getActionIcon(entry.action)}
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getActionColor(entry.action)}`}>
                  {entry.action.replace('_', ' ').toUpperCase()}
                </span>
                
                <span className="text-sm text-gray-500 flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  {formatTimestamp(entry.timestamp)}
                </span>
              </div>

              <div className="flex items-center space-x-4 text-xs text-gray-500">
                {entry.userId && (
                  <span className="flex items-center">
                    <User className="w-3 h-3 mr-1" />
                    {entry.userId}
                  </span>
                )}
                
                {entry.ipAddress && (
                  <span className="flex items-center">
                    <MapPin className="w-3 h-3 mr-1" />
                    {entry.ipAddress}
                  </span>
                )}
                
                {entry.userAgent && (
                  <span className="flex items-center">
                    <Monitor className="w-3 h-3 mr-1" />
                    {entry.userAgent.substring(0, 50)}...
                  </span>
                )}
              </div>
            </div>
          </div>

          {hasDetails && (
            <button
              onClick={() => toggleEntryExpansion(entry.id)}
              className="ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              {isExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </button>
          )}
        </div>

        {isExpanded && hasDetails && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {entry.oldValues && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Old Values</h4>
                  <pre className="bg-gray-50 p-3 rounded text-xs overflow-x-auto">
                    {JSON.stringify(entry.oldValues, null, 2)}
                  </pre>
                </div>
              )}
              
              {entry.newValues && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">New Values</h4>
                  <pre className="bg-gray-50 p-3 rounded text-xs overflow-x-auto">
                    {JSON.stringify(entry.newValues, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderPagination = () => {
    const totalPages = Math.ceil(totalEntries / pageSize);
    
    if (totalPages <= 1) return null;

    return (
      <div className="flex items-center justify-between mt-6">
        <div className="text-sm text-gray-700">
          Showing {((page - 1) * pageSize) + 1} to {Math.min(page * pageSize, totalEntries)} of {totalEntries} entries
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setPage(page - 1)}
            disabled={page === 1}
            className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <span className="px-3 py-1 text-sm">
            Page {page} of {totalPages}
          </span>
          
          <button
            onClick={() => setPage(page + 1)}
            disabled={page === totalPages}
            className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      </div>
    );
  };

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center">
          <Activity className="w-5 h-5 text-red-600 mr-2" />
          <span className="text-red-800">Error loading audit trail: {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Activity className="w-5 h-5 mr-2" />
          Audit Trail
        </h3>
        
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
        >
          <Filter className="w-4 h-4 mr-2" />
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </button>
      </div>

      {renderFilters()}

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading audit trail...</span>
        </div>
      ) : entries.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <Activity className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>No audit entries found</p>
        </div>
      ) : (
        <>
          <div className="space-y-3">
            {entries.map(renderEntry)}
          </div>
          {renderPagination()}
        </>
      )}
    </div>
  );
}
