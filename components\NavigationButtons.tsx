interface NavigationButtonsProps {
  currentIndex: number;
  totalQuestions: number;
  canGoNext: boolean;
  onPrevious: () => void;
  onNext: () => void;
}

export default function NavigationButtons({
  currentIndex,
  totalQuestions,
  canGoNext,
  onPrevious,
  onNext
}: NavigationButtonsProps) {
  const isFirstQuestion = currentIndex === 0;
  const isLastQuestion = currentIndex === totalQuestions - 1;

  return (
    <div className="flex justify-between items-center mt-6">
      <button
        onClick={onPrevious}
        disabled={isFirstQuestion}
        className={`
          px-4 py-2 text-sm font-medium rounded-md transition-colors
          ${isFirstQuestion
            ? 'text-gray-300 cursor-not-allowed'
            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
          }
        `}
      >
        Kembali
      </button>

      <span className="text-xs text-gray-400 font-medium">
        {currentIndex + 1} dari {totalQuestions}
      </span>

      <button
        onClick={onNext}
        disabled={!canGoNext}
        className={`
          px-4 py-2 text-sm font-medium rounded-md transition-colors
          ${!canGoNext
            ? 'text-gray-300 cursor-not-allowed'
            : isLastQuestion
              ? 'text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50'
              : 'text-neutral-600 hover:text-neutral-900 hover:bg-blue-50'
          }
        `}
      >
        {isLastQuestion ? 'Selesai' : 'Lanjut'}
      </button>
    </div>
  );
}