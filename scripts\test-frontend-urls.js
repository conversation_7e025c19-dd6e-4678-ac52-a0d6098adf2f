const http = require('http');

// Helper function to make HTTP requests
function makeRequest(options) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });

    req.on('error', reject);
    req.end();
  });
}

async function testFrontendUrls() {
  console.log('🧪 Testing Frontend URLs...\n');

  const urls = [
    { path: '/', name: 'Home Page' },
    { path: '/test', name: 'Test Page' },
    { path: '/login', name: '<PERSON>gin Page' },
    { path: '/register', name: 'Register Page' },
    { path: '/dashboard', name: 'Dashboard Page' },
    { path: '/history', name: 'History Page' },
    { path: '/user-profile', name: 'User Profile Page' },
    { path: '/demo-visualization', name: 'Demo Visualization Page' },
    { path: '/result/test-id', name: 'Result Page (with test ID)' },
  ];

  try {
    for (const url of urls) {
      console.log(`Testing ${url.name} (${url.path})...`);
      
      const options = {
        hostname: 'localhost',
        port: 3000,
        path: url.path,
        method: 'GET',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'User-Agent': 'Test-Agent/1.0'
        },
      };

      try {
        const response = await makeRequest(options);
        
        if (response.status === 200) {
          console.log(`✅ ${url.name} - OK (${response.status})`);
        } else if (response.status === 404) {
          console.log(`⚠️ ${url.name} - Not Found (${response.status})`);
        } else if (response.status >= 300 && response.status < 400) {
          console.log(`🔄 ${url.name} - Redirect (${response.status})`);
        } else {
          console.log(`❌ ${url.name} - Error (${response.status})`);
        }
      } catch (error) {
        console.log(`❌ ${url.name} - Request failed: ${error.message}`);
      }
    }

    console.log('\n🎉 Frontend URL testing completed!');
    console.log('\nNote: Some pages may show redirects or errors if they require authentication.');
    console.log('This is expected behavior for protected routes.');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testFrontendUrls();
