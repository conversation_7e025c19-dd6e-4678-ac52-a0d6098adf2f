'use client';

import React, { useState } from 'react';
import { Download, FileText, Table, Calendar, Filter, Loader2 } from 'lucide-react';

interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  dateRange: {
    start: string;
    end: string;
  };
  dataTypes: {
    analytics: boolean;
    performance: boolean;
    userBehavior: boolean;
    cacheStats: boolean;
    systemHealth: boolean;
  };
  includeCharts: boolean;
  includeRawData: boolean;
}

interface ExportAnalyticsProps {
  className?: string;
}

export function ExportAnalytics({ className }: ExportAnalyticsProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'excel',
    dateRange: {
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end: new Date().toISOString().split('T')[0],
    },
    dataTypes: {
      analytics: true,
      performance: true,
      userBehavior: true,
      cacheStats: true,
      systemHealth: false,
    },
    includeCharts: true,
    includeRawData: false,
  });
  const [lastExport, setLastExport] = useState<string | null>(null);

  const handleExport = async () => {
    setIsExporting(true);

    try {
      const response = await fetch('/api/analytics/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(exportOptions),
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Handle file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `analytics-export-${timestamp}.${exportOptions.format}`;
      a.download = filename;
      
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setLastExport(new Date().toLocaleString('id-ID'));
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const updateDataType = (type: keyof ExportOptions['dataTypes'], value: boolean) => {
    setExportOptions(prev => ({
      ...prev,
      dataTypes: {
        ...prev.dataTypes,
        [type]: value,
      },
    }));
  };

  const getSelectedDataTypesCount = () => {
    return Object.values(exportOptions.dataTypes).filter(Boolean).length;
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'csv':
        return <Table className="w-4 h-4" />;
      case 'excel':
        return <Table className="w-4 h-4" />;
      case 'pdf':
        return <FileText className="w-4 h-4" />;
      default:
        return <Download className="w-4 h-4" />;
    }
  };

  const isExportDisabled = () => {
    return getSelectedDataTypesCount() === 0 || !exportOptions.dateRange.start || !exportOptions.dateRange.end;
  };

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Download className="w-5 h-5 mr-2" />
          Export Analytics
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Export analytics data in various formats for reporting and analysis
        </p>
      </div>

      <div className="p-6 space-y-6">
        {/* Export Format */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Export Format
          </label>
          <div className="grid grid-cols-3 gap-3">
            {(['csv', 'excel', 'pdf'] as const).map((format) => (
              <button
                key={format}
                onClick={() => setExportOptions(prev => ({ ...prev, format }))}
                className={`p-3 border rounded-lg flex items-center justify-center space-x-2 transition-colors ${
                  exportOptions.format === format
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                {getFormatIcon(format)}
                <span className="font-medium uppercase">{format}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Date Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            <Calendar className="w-4 h-4 inline mr-1" />
            Date Range
          </label>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-xs text-gray-500 mb-1">Start Date</label>
              <input
                type="date"
                value={exportOptions.dateRange.start}
                onChange={(e) => setExportOptions(prev => ({
                  ...prev,
                  dateRange: { ...prev.dateRange, start: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">End Date</label>
              <input
                type="date"
                value={exportOptions.dateRange.end}
                onChange={(e) => setExportOptions(prev => ({
                  ...prev,
                  dateRange: { ...prev.dateRange, end: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Data Types */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            <Filter className="w-4 h-4 inline mr-1" />
            Data to Include ({getSelectedDataTypesCount()} selected)
          </label>
          <div className="space-y-3">
            {Object.entries({
              analytics: 'General Analytics',
              performance: 'Performance Metrics',
              userBehavior: 'User Behavior',
              cacheStats: 'Cache Statistics',
              systemHealth: 'System Health',
            }).map(([key, label]) => (
              <label key={key} className="flex items-center">
                <input
                  type="checkbox"
                  checked={exportOptions.dataTypes[key as keyof ExportOptions['dataTypes']]}
                  onChange={(e) => updateDataType(key as keyof ExportOptions['dataTypes'], e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">{label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Additional Options */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Additional Options
          </label>
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={exportOptions.includeCharts}
                onChange={(e) => setExportOptions(prev => ({ ...prev, includeCharts: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                disabled={exportOptions.format === 'csv'}
              />
              <span className="ml-2 text-sm text-gray-700">
                Include Charts and Visualizations
                {exportOptions.format === 'csv' && (
                  <span className="text-gray-400 text-xs ml-1">(Not available for CSV)</span>
                )}
              </span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={exportOptions.includeRawData}
                onChange={(e) => setExportOptions(prev => ({ ...prev, includeRawData: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">
                Include Raw Data Tables
              </span>
            </label>
          </div>
        </div>

        {/* Export Button */}
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={handleExport}
            disabled={isExporting || isExportDisabled()}
            className={`w-full flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition-colors ${
              isExporting || isExportDisabled()
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
            }`}
          >
            {isExporting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Export Analytics Data
              </>
            )}
          </button>
          
          {isExportDisabled() && (
            <p className="text-xs text-red-600 mt-2 text-center">
              Please select at least one data type and valid date range
            </p>
          )}
        </div>

        {/* Last Export Info */}
        {lastExport && (
          <div className="bg-green-50 border border-green-200 rounded-md p-3">
            <div className="flex items-center">
              <Download className="w-4 h-4 text-green-600 mr-2" />
              <span className="text-sm text-green-800">
                Last export completed: {lastExport}
              </span>
            </div>
          </div>
        )}

        {/* Export Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <h4 className="text-sm font-medium text-blue-800 mb-2">Export Tips:</h4>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• CSV format is best for data analysis in spreadsheet applications</li>
            <li>• Excel format includes formatted tables and basic charts</li>
            <li>• PDF format provides a comprehensive report with visualizations</li>
            <li>• Large date ranges may take longer to process</li>
            <li>• System health data is only available for the last 30 days</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
