import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
} from '@/lib/api-utils';
import { enhancedDb } from '@/lib/server/enhanced-database';
import { withMiddleware } from '@/lib/server/middleware';

// POST - Clear all cache entries
async function postHandler(request: NextRequest) {
  try {
    // Clear all cache entries
    const deletedCount = await enhancedDb.clearCache();

    // Also clear Gemini service cache if available
    try {
      const { EnhancedGeminiProfileService } = await import('@/lib/server/enhanced-gemini-service');
      const geminiService = EnhancedGeminiProfileService.getInstance();
      await geminiService.clearCache();
    } catch (error) {
      console.warn('Could not clear Gemini service cache:', error);
    }

    const response = {
      message: 'Cache cleared successfully',
      deletedCount,
      timestamp: new Date().toISOString(),
    };

    return createSuccessResponse(response);

  } catch (error) {
    console.error('Clear cache error:', error);
    
    return createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to clear cache',
      500
    );
  }
}

export const POST = withMiddleware(post<PERSON>andler);
