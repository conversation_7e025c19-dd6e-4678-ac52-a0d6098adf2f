'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/store/authStore';

export default function HistoryPage() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const assessments = user.assessments;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Riwayat Tes</h1>
              <p className="text-gray-600">Semua hasil tes kepribadian dan minat karir Anda</p>
            </div>
            <div className="flex space-x-3">
              <Link
                href="/dashboard"
                className="text-gray-600 hover:text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Dashboard
              </Link>
              <Link
                href="/test"
                className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Tes Baru
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="text-center">
              <p className="text-3xl font-bold text-indigo-600">{assessments.length}</p>
              <p className="text-sm text-gray-600 mt-1">Total Tes</p>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="text-center">
              <p className="text-3xl font-bold text-green-600">
                {assessments.filter(a => a.profileGenerated).length}
              </p>
              <p className="text-sm text-gray-600 mt-1">Profil Lengkap</p>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="text-center">
              <p className="text-3xl font-bold text-blue-600">
                {assessments.length > 0 ? 
                  Math.round((assessments.filter(a => a.profileGenerated).length / assessments.length) * 100) : 0
                }%
              </p>
              <p className="text-sm text-gray-600 mt-1">Tingkat Kelengkapan</p>
            </div>
          </div>
        </div>

        {/* Assessment List */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Semua Hasil Tes ({assessments.length})
            </h2>
          </div>
          
          {assessments.length === 0 ? (
            <div className="p-8 text-center">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada riwayat tes</h3>
              <p className="text-gray-600 mb-4">Mulai tes pertama Anda untuk melihat profil kepribadian dan minat karir.</p>
              <Link
                href="/test"
                className="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Mulai Tes Sekarang
              </Link>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {assessments.map((assessment, index) => (
                <div key={assessment.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className={`w-3 h-3 rounded-full ${
                          assessment.profileGenerated ? 'bg-green-400' : 'bg-yellow-400'
                        }`}></div>
                        <span className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          #{assessments.length - index}
                        </span>
                        <h3 className="text-lg font-medium text-gray-900">
                          {assessment.profile?.profileTitle || 'Tes Kepribadian'}
                        </h3>
                      </div>
                      
                      <div className="text-sm text-gray-600 mb-2">
                        <span className="font-medium">Tanggal:</span>{' '}
                        {new Date(assessment.createdAt).toLocaleDateString('id-ID', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                      
                      {assessment.profile?.profileDescription && (
                        <p className="text-sm text-gray-500 line-clamp-2 mb-2">
                          {assessment.profile.profileDescription}
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span className={`px-2 py-1 rounded-full ${
                          assessment.profileGenerated 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {assessment.profileGenerated ? 'Profil Lengkap' : 'Menunggu Profil'}
                        </span>
                        <span>ID: {assessment.resultId}</span>
                      </div>
                    </div>
                    
                    <div className="ml-6 flex flex-col space-y-2">
                      <Link
                        href={`/result/${assessment.resultId}`}
                        className="text-indigo-600 hover:text-indigo-700 text-sm font-medium text-center px-4 py-2 border border-indigo-600 rounded-lg hover:bg-indigo-50 transition-colors"
                      >
                        Lihat Detail
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
