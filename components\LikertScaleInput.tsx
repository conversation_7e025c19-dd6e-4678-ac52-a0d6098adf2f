import { likertOptions } from '@/lib/riasecQuestions';

interface LikertScaleInputProps {
  questionId: number;
  selectedValue?: number;
  onSelect: (questionId: number, value: number) => void;
}

export default function LikertScaleInput({
  questionId,
  selectedValue,
  onSelect
}: LikertScaleInputProps) {
  return (
    <div className="space-y-6">

      <div className="px-4 py-4">
        {/* Labels */}
        <div className="flex justify-between items-center mb-6">
          <span className="text-xs text-gray-500">Tidak Setuju</span>
          <span className="text-xs text-gray-500">Setuju</span>
        </div>

        {/* Radio buttons */}
        <div className="flex justify-between items-center">
          {likertOptions.map((option) => (
            <label
              key={option.value}
              className="flex flex-col items-center cursor-pointer group"
            >
              <div className="relative">
                <input
                  type="radio"
                  name={`question-${questionId}`}
                  value={option.value}
                  checked={selectedValue === option.value}
                  onChange={() => onSelect(questionId, option.value)}
                  className="sr-only"
                />
                
                <div className={`
                  w-6 h-6 rounded-full border-2 transition-all duration-200 flex items-center justify-center
                  ${selectedValue === option.value 
                    ? 'border-gray-800 bg-gray-800' 
                    : 'border-gray-300 bg-white hover:border-gray-400'
                  }
                `}>
                  {selectedValue === option.value && (
                    <div className="w-2 h-2 bg-white rounded-full" />
                  )}
                </div>
              </div>

              <span className={`
                mt-2 text-xs transition-colors duration-200
                ${selectedValue === option.value 
                  ? 'text-gray-700 font-medium' 
                  : 'text-gray-400 group-hover:text-gray-600'
                }
              `}>
                {option.value}
              </span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
}