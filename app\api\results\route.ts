import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse
} from '@/lib/api-utils';
import { ResultsResponse } from '@/lib/api-types';
import { EnhancedGeminiProfileService } from '@/lib/server/enhanced-gemini-service';
import { RiasecScores, OceanScores } from '@/lib/types';
import { enhancedDb } from '@/lib/server/enhanced-database';
import { withMiddleware } from '@/lib/server/middleware';

// Helper function to get user ID from session
async function getUserIdFromSession(req: NextRequest): Promise<string | null> {
  const sessionCookie = req.cookies.get('atma_session');
  if (!sessionCookie) return null;

  try {
    const session = JSON.parse(sessionCookie.value);
    return session.userId || null;
  } catch {
    return null;
  }
}

async function postHandler(request: NextRequest) {
  const { searchParams } = new URL(request.url);

  // Get scores from query parameters with validation
  const r = parseInt(searchParams.get('r') || '0');
  const i = parseInt(searchParams.get('i') || '0');
  const a = parseInt(searchParams.get('a') || '0');
  const s = parseInt(searchParams.get('s') || '0');
  const e = parseInt(searchParams.get('e') || '0');
  const c = parseInt(searchParams.get('c') || '0');

  const o = parseInt(searchParams.get('o') || '0');
  const ocean_c = parseInt(searchParams.get('ocean_c') || '0');
  const ocean_e = parseInt(searchParams.get('ocean_e') || '0');
  const ocean_a = parseInt(searchParams.get('ocean_a') || '0');
  const n = parseInt(searchParams.get('n') || '0');

  // Validate RIASEC scores (each should be between 0-30 for 6 questions with 1-5 scale)
  if ([r, i, a, s, e, c].some(score => score < 0 || score > 30 || isNaN(score))) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid RIASEC scores. Each score must be between 0-30',
      400
    );
  }

  // Validate OCEAN scores (each should be between 5-25 for 5 questions with 1-5 scale)
  if ([o, ocean_c, ocean_e, ocean_a, n].some(score => score < 5 || score > 25 || isNaN(score))) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid OCEAN scores. Each score must be between 5-25',
      400
    );
  }

  const riasecScores: RiasecScores = { R: r, I: i, A: a, S: s, E: e, C: c };
  const oceanScores: OceanScores = { O: o, C: ocean_c, E: ocean_e, A: ocean_a, N: n };

  // Get user ID from session (if logged in)
  const userId = await getUserIdFromSession(request);

  // Create assessment in database with audit trail
  const dbAssessment = await enhancedDb.createAssessment(
    riasecScores,
    oceanScores,
    userId || undefined,
    {
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    }
  );

  // Generate profile using Enhanced Gemini service (includes caching)
  const geminiService = EnhancedGeminiProfileService.getInstance();

  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Profile generation timeout')), 45000);
  });

  const aiProfile = await Promise.race([
    geminiService.generateCombinedProfile(riasecScores, oceanScores, { priority: 'high' }),
    timeoutPromise
  ]);

  // Save profile to database with normalized data
  await enhancedDb.createProfile(dbAssessment.id, aiProfile as any, userId || undefined);

  const assessment = {
    id: dbAssessment.id,
    resultId: dbAssessment.resultId || dbAssessment.id, // Fallback to id if resultId is null
    riasecScores,
    oceanScores,
    createdAt: dbAssessment.createdAt.toISOString(),
    profileGenerated: true,
  };

  const profile = {
    ...aiProfile as any,
    generatedAt: new Date().toISOString(),
  };

  const response: ResultsResponse = {
    assessment,
    profile,
  };

  return createSuccessResponse(response);
}

// GET handler for retrieving results by resultId
async function getResultHandler(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const resultId = searchParams.get('resultId');

  if (!resultId) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Result ID is required',
      400
    );
  }

  // Get assessment and profile by resultId
  const assessmentData = await db.getAssessmentByResultId(resultId);

  if (!assessmentData) {
    return createErrorResponse(
      'NOT_FOUND',
      'Result not found',
      404
    );
  }

  if (!assessmentData.profile) {
    return createErrorResponse(
      'NOT_FOUND',
      'Profile not generated for this assessment',
      404
    );
  }

  const assessment = {
    id: assessmentData.id,
    resultId: assessmentData.resultId || assessmentData.id,
    riasecScores: {
      R: assessmentData.riasecR,
      I: assessmentData.riasecI,
      A: assessmentData.riasecA,
      S: assessmentData.riasecS,
      E: assessmentData.riasecE,
      C: assessmentData.riasecC,
    },
    oceanScores: {
      O: assessmentData.oceanO,
      C: assessmentData.oceanC,
      E: assessmentData.oceanE,
      A: assessmentData.oceanA,
      N: assessmentData.oceanN,
    },
    createdAt: assessmentData.createdAt.toISOString(),
    profileGenerated: true,
  };

  const profile = {
    profileTitle: assessmentData.profile.profileTitle,
    profileDescription: assessmentData.profile.profileDescription,
    strengths: JSON.parse(assessmentData.profile.strengths),
    careerSuggestions: JSON.parse(assessmentData.profile.careerSuggestions),
    workEnvironment: assessmentData.profile.workEnvironment,
    developmentAreas: JSON.parse(assessmentData.profile.developmentAreas),
    personalityInsights: JSON.parse(assessmentData.profile.personalityInsights),
    careerFit: assessmentData.profile.careerFit,
    generatedAt: assessmentData.profile.generatedAt.toISOString(),
  };

  const response: ResultsResponse = {
    assessment,
    profile,
  };

  return createSuccessResponse(response);
}

export const GET = withMiddleware(getResultHandler);
export const POST = withMiddleware(postHandler);
