const { PrismaClient } = require('../lib/generated/prisma');
const readline = require('readline');

const prisma = new PrismaClient();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Fungsi untuk menampilkan menu
function showMenu() {
  console.log('\n🔧 Data Manager - Manual Data Operations');
  console.log('=====================================');
  console.log('1. 📊 View specific assessment');
  console.log('2. ✏️  Edit assessment scores');
  console.log('3. 🗑️  Delete specific assessment');
  console.log('4. 📝 Edit profile data');
  console.log('5. 🔍 Search assessments by date range');
  console.log('6. 📋 List all assessments with details');
  console.log('7. 🧹 Delete assessments by date range');
  console.log('8. 📊 Database statistics');
  console.log('0. 🚪 Exit');
  console.log('=====================================');
}

// Fungsi untuk melihat assessment spesifik
async function viewAssessment() {
  const id = await askQuestion('Enter assessment ID: ');
  
  try {
    const assessment = await prisma.assessment.findUnique({
      where: { id },
      include: { profile: true }
    });
    
    if (!assessment) {
      console.log('❌ Assessment not found');
      return;
    }
    
    console.log('\n📊 Assessment Details:');
    console.log('======================');
    console.log(`ID: ${assessment.id}`);
    console.log(`Created: ${assessment.createdAt}`);
    console.log(`RIASEC Scores: R:${assessment.riasecR}, I:${assessment.riasecI}, A:${assessment.riasecA}, S:${assessment.riasecS}, E:${assessment.riasecE}, C:${assessment.riasecC}`);
    console.log(`OCEAN Scores: O:${assessment.oceanO}, C:${assessment.oceanC}, E:${assessment.oceanE}, A:${assessment.oceanA}, N:${assessment.oceanN}`);
    console.log(`Has Profile: ${assessment.profile ? '✅ Yes' : '❌ No'}`);
    
    if (assessment.profile) {
      console.log(`Profile Title: ${assessment.profile.profileTitle}`);
      console.log(`Generated: ${assessment.profile.generatedAt}`);
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Fungsi untuk edit skor assessment
async function editAssessmentScores() {
  const id = await askQuestion('Enter assessment ID to edit: ');
  
  try {
    const assessment = await prisma.assessment.findUnique({
      where: { id }
    });
    
    if (!assessment) {
      console.log('❌ Assessment not found');
      return;
    }
    
    console.log('\n📊 Current Scores:');
    console.log(`RIASEC: R:${assessment.riasecR}, I:${assessment.riasecI}, A:${assessment.riasecA}, S:${assessment.riasecS}, E:${assessment.riasecE}, C:${assessment.riasecC}`);
    console.log(`OCEAN: O:${assessment.oceanO}, C:${assessment.oceanC}, E:${assessment.oceanE}, A:${assessment.oceanA}, N:${assessment.oceanN}`);
    
    const confirm = await askQuestion('\nDo you want to edit these scores? (y/n): ');
    if (confirm.toLowerCase() !== 'y') return;
    
    console.log('\nEnter new scores (press Enter to keep current value):');
    
    // RIASEC scores
    const newR = await askQuestion(`RIASEC R (current: ${assessment.riasecR}): `) || assessment.riasecR;
    const newI = await askQuestion(`RIASEC I (current: ${assessment.riasecI}): `) || assessment.riasecI;
    const newA = await askQuestion(`RIASEC A (current: ${assessment.riasecA}): `) || assessment.riasecA;
    const newS = await askQuestion(`RIASEC S (current: ${assessment.riasecS}): `) || assessment.riasecS;
    const newE = await askQuestion(`RIASEC E (current: ${assessment.riasecE}): `) || assessment.riasecE;
    const newC = await askQuestion(`RIASEC C (current: ${assessment.riasecC}): `) || assessment.riasecC;
    
    // OCEAN scores
    const newO = await askQuestion(`OCEAN O (current: ${assessment.oceanO}): `) || assessment.oceanO;
    const newOceanC = await askQuestion(`OCEAN C (current: ${assessment.oceanC}): `) || assessment.oceanC;
    const newOceanE = await askQuestion(`OCEAN E (current: ${assessment.oceanE}): `) || assessment.oceanE;
    const newOceanA = await askQuestion(`OCEAN A (current: ${assessment.oceanA}): `) || assessment.oceanA;
    const newN = await askQuestion(`OCEAN N (current: ${assessment.oceanN}): `) || assessment.oceanN;
    
    // Update assessment
    await prisma.assessment.update({
      where: { id },
      data: {
        riasecR: parseInt(newR),
        riasecI: parseInt(newI),
        riasecA: parseInt(newA),
        riasecS: parseInt(newS),
        riasecE: parseInt(newE),
        riasecC: parseInt(newC),
        oceanO: parseInt(newO),
        oceanC: parseInt(newOceanC),
        oceanE: parseInt(newOceanE),
        oceanA: parseInt(newOceanA),
        oceanN: parseInt(newN),
        updatedAt: new Date()
      }
    });
    
    console.log('✅ Assessment scores updated successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Fungsi untuk hapus assessment spesifik
async function deleteSpecificAssessment() {
  const id = await askQuestion('Enter assessment ID to delete: ');
  
  try {
    const assessment = await prisma.assessment.findUnique({
      where: { id },
      include: { profile: true }
    });
    
    if (!assessment) {
      console.log('❌ Assessment not found');
      return;
    }
    
    console.log('\n⚠️  Assessment to be deleted:');
    console.log(`ID: ${assessment.id}`);
    console.log(`Created: ${assessment.createdAt}`);
    console.log(`Has Profile: ${assessment.profile ? 'Yes' : 'No'}`);
    
    const confirm = await askQuestion('\nAre you sure you want to delete this assessment? (type "DELETE" to confirm): ');
    if (confirm !== 'DELETE') {
      console.log('❌ Deletion cancelled');
      return;
    }
    
    await prisma.assessment.delete({
      where: { id }
    });
    
    console.log('✅ Assessment deleted successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Fungsi untuk list semua assessments
async function listAllAssessments() {
  try {
    const assessments = await prisma.assessment.findMany({
      include: { profile: true },
      orderBy: { createdAt: 'desc' }
    });
    
    console.log(`\n📋 All Assessments (${assessments.length} total):`);
    console.log('='.repeat(80));
    
    assessments.forEach((assessment, index) => {
      const hasProfile = assessment.profile ? '✅' : '❌';
      const date = assessment.createdAt.toLocaleDateString();
      console.log(`${index + 1}. ${assessment.id} | ${date} | Profile: ${hasProfile}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Fungsi untuk statistik database
async function showDatabaseStats() {
  try {
    const userCount = await prisma.user.count();
    const assessmentCount = await prisma.assessment.count();
    const profileCount = await prisma.profile.count();
    
    console.log('\n📊 Database Statistics:');
    console.log('=======================');
    console.log(`👥 Users: ${userCount}`);
    console.log(`📝 Assessments: ${assessmentCount}`);
    console.log(`🎯 Profiles: ${profileCount}`);
    console.log(`📈 Profile Generation Rate: ${assessmentCount > 0 ? Math.round((profileCount / assessmentCount) * 100) : 0}%`);
    
    // Recent activity
    const recentAssessments = await prisma.assessment.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' }
    });
    
    if (recentAssessments.length > 0) {
      console.log('\n📅 Recent Activity:');
      recentAssessments.forEach((assessment, index) => {
        console.log(`  ${index + 1}. ${assessment.createdAt.toLocaleDateString()} - ${assessment.id.substring(0, 8)}...`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Main function
async function main() {
  console.log('🚀 Starting Data Manager...');
  
  try {
    while (true) {
      showMenu();
      const choice = await askQuestion('\nSelect an option: ');
      
      switch (choice) {
        case '1':
          await viewAssessment();
          break;
        case '2':
          await editAssessmentScores();
          break;
        case '3':
          await deleteSpecificAssessment();
          break;
        case '4':
          console.log('🚧 Profile editing feature coming soon...');
          break;
        case '5':
          console.log('🚧 Date range search feature coming soon...');
          break;
        case '6':
          await listAllAssessments();
          break;
        case '7':
          console.log('🚧 Date range deletion feature coming soon...');
          break;
        case '8':
          await showDatabaseStats();
          break;
        case '0':
          console.log('👋 Goodbye!');
          return;
        default:
          console.log('❌ Invalid option');
      }
      
      await askQuestion('\nPress Enter to continue...');
    }
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
    rl.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}
