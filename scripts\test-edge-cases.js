const http = require('http');

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = {
            status: res.statusCode,
            headers: res.headers,
            data: body ? JSON.parse(body) : null
          };
          resolve(response);
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: body
          });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testEdgeCases() {
  console.log('🧪 Testing Edge Cases and Error Handling...\n');

  try {
    // Test 1: Register with duplicate email
    console.log('1️⃣ Testing duplicate email registration...');
    const duplicateEmail = '<EMAIL>';
    
    // First registration
    const registerOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/register',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    };

    await makeRequest(registerOptions, { email: duplicateEmail, name: 'First User' });
    
    // Second registration with same email
    const duplicateResponse = await makeRequest(registerOptions, { 
      email: duplicateEmail, 
      name: 'Second User' 
    });
    
    if (duplicateResponse.status === 409) {
      console.log('✅ Duplicate email properly rejected');
      console.log('   Status:', duplicateResponse.status);
      console.log('   Error:', duplicateResponse.data.error);
    } else {
      console.log('❌ Duplicate email not properly handled');
    }

    // Test 2: Login with non-existent email
    console.log('\n2️⃣ Testing login with non-existent email...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/login',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    };

    const nonExistentResponse = await makeRequest(loginOptions, { 
      email: '<EMAIL>' 
    });
    
    if (nonExistentResponse.status === 404) {
      console.log('✅ Non-existent email properly rejected');
      console.log('   Status:', nonExistentResponse.status);
      console.log('   Error:', nonExistentResponse.data.error);
    } else {
      console.log('❌ Non-existent email not properly handled');
    }

    // Test 3: Invalid email format
    console.log('\n3️⃣ Testing invalid email format...');
    const invalidEmailResponse = await makeRequest(registerOptions, { 
      email: 'invalid-email', 
      name: 'Test User' 
    });
    
    if (invalidEmailResponse.status === 400) {
      console.log('✅ Invalid email format properly rejected');
      console.log('   Status:', invalidEmailResponse.status);
      console.log('   Error:', invalidEmailResponse.data.error);
    } else {
      console.log('❌ Invalid email format not properly handled');
    }

    // Test 4: Missing required fields
    console.log('\n4️⃣ Testing missing required fields...');
    const missingFieldsResponse = await makeRequest(registerOptions, { 
      email: '<EMAIL>' 
      // Missing name field
    });
    
    if (missingFieldsResponse.status === 400) {
      console.log('✅ Missing required fields properly rejected');
      console.log('   Status:', missingFieldsResponse.status);
      console.log('   Error:', missingFieldsResponse.data.error);
    } else {
      console.log('❌ Missing required fields not properly handled');
    }

    // Test 5: Invalid assessment scores
    console.log('\n5️⃣ Testing invalid assessment scores...');
    const invalidScoresParams = new URLSearchParams({
      r: '50', i: '60', a: '70', s: '80', e: '90', c: '100', // Invalid RIASEC scores (too high)
      o: '30', ocean_c: '35', ocean_e: '40', ocean_a: '45', n: '50' // Invalid OCEAN scores (too high)
    });

    const invalidScoresOptions = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/results?${invalidScoresParams.toString()}`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    };

    const invalidScoresResponse = await makeRequest(invalidScoresOptions);
    
    if (invalidScoresResponse.status === 400) {
      console.log('✅ Invalid assessment scores properly rejected');
      console.log('   Status:', invalidScoresResponse.status);
      console.log('   Error:', invalidScoresResponse.data.error);
    } else {
      console.log('❌ Invalid assessment scores not properly handled');
    }

    // Test 6: Access protected route without authentication
    console.log('\n6️⃣ Testing protected route without authentication...');
    const protectedOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/profile',
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    };

    const protectedResponse = await makeRequest(protectedOptions);
    
    if (protectedResponse.status === 401) {
      console.log('✅ Protected route properly secured');
      console.log('   Status:', protectedResponse.status);
      console.log('   Error:', protectedResponse.data.error);
    } else {
      console.log('❌ Protected route not properly secured');
    }

    // Test 7: Rate limiting (make multiple rapid requests)
    console.log('\n7️⃣ Testing rate limiting...');
    const rapidRequests = [];
    for (let i = 0; i < 10; i++) {
      rapidRequests.push(makeRequest({
        hostname: 'localhost',
        port: 3000,
        path: '/api/health',
        method: 'GET',
      }));
    }

    const rapidResults = await Promise.all(rapidRequests);
    const rateLimitedRequests = rapidResults.filter(r => r.status === 429);
    
    if (rateLimitedRequests.length > 0) {
      console.log('✅ Rate limiting is working');
      console.log('   Rate limited requests:', rateLimitedRequests.length);
    } else {
      console.log('ℹ️ Rate limiting not triggered (may need more requests)');
    }

    console.log('\n🎉 Edge case testing completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testEdgeCases();
