const { PrismaClient, Prisma } = require('../lib/generated/prisma');

async function addDataValidationConstraints() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 Adding Data Validation Constraints');
    console.log('📋 This will add CHECK constraints for data integrity');
    
    // Add RIASEC score constraints (0-30)
    console.log('\n🎯 Adding RIASEC score validation (0-30)...');
    
    await prisma.$executeRaw`
      CREATE TRIGGER IF NOT EXISTS validate_riasec_r_insert
      BEFORE INSERT ON assessments
      FOR EACH ROW
      BEGIN
        SELECT CASE 
          WHEN NEW.riasecR < 0 OR NEW.riasecR > 30 
          THEN RAISE(ABORT, 'RIASEC R score must be between 0 and 30')
        END;
      END
    `;
    
    await prisma.$executeRaw`
      CREATE TRIGGER IF NOT EXISTS validate_riasec_r_update
      BEFORE UPDATE ON assessments
      FOR EACH ROW
      BEGIN
        SELECT CASE 
          WHEN NEW.riasecR < 0 OR NEW.riasecR > 30 
          THEN RAISE(ABORT, 'RIASEC R score must be between 0 and 30')
        END;
      END
    `;
    
    // Similar triggers for other RIASEC dimensions
    const riasecDimensions = ['I', 'A', 'S', 'E', 'C'];
    for (const dim of riasecDimensions) {
      const insertTriggerName = `validate_riasec_${dim.toLowerCase()}_insert`;
      const updateTriggerName = `validate_riasec_${dim.toLowerCase()}_update`;
      const columnName = `riasec${dim}`;

      await prisma.$executeRaw(Prisma.sql`
        CREATE TRIGGER IF NOT EXISTS ${Prisma.raw(insertTriggerName)}
        BEFORE INSERT ON assessments
        FOR EACH ROW
        BEGIN
          SELECT CASE
            WHEN NEW.${Prisma.raw(columnName)} < 0 OR NEW.${Prisma.raw(columnName)} > 30
            THEN RAISE(ABORT, 'RIASEC ${Prisma.raw(dim)} score must be between 0 and 30')
          END;
        END
      `);

      await prisma.$executeRaw(Prisma.sql`
        CREATE TRIGGER IF NOT EXISTS ${Prisma.raw(updateTriggerName)}
        BEFORE UPDATE ON assessments
        FOR EACH ROW
        BEGIN
          SELECT CASE
            WHEN NEW.${Prisma.raw(columnName)} < 0 OR NEW.${Prisma.raw(columnName)} > 30
            THEN RAISE(ABORT, 'RIASEC ${Prisma.raw(dim)} score must be between 0 and 30')
          END;
        END
      `);
    }
    
    console.log('✅ RIASEC validation constraints added');
    
    // Add OCEAN score constraints (5-25)
    console.log('\n🌊 Adding OCEAN score validation (5-25)...');
    
    const oceanDimensions = ['O', 'C', 'E', 'A', 'N'];
    for (const dim of oceanDimensions) {
      await prisma.$executeRaw`
        CREATE TRIGGER IF NOT EXISTS ${`validate_ocean_${dim.toLowerCase()}_insert`}
        BEFORE INSERT ON assessments
        FOR EACH ROW
        BEGIN
          SELECT CASE 
            WHEN NEW.ocean${dim} < 5 OR NEW.ocean${dim} > 25 
            THEN RAISE(ABORT, 'OCEAN ${dim} score must be between 5 and 25')
          END;
        END
      `;
      
      await prisma.$executeRaw`
        CREATE TRIGGER IF NOT EXISTS ${`validate_ocean_${dim.toLowerCase()}_update`}
        BEFORE UPDATE ON assessments
        FOR EACH ROW
        BEGIN
          SELECT CASE 
            WHEN NEW.ocean${dim} < 5 OR NEW.ocean${dim} > 25 
            THEN RAISE(ABORT, 'OCEAN ${dim} score must be between 5 and 25')
          END;
        END
      `;
    }
    
    console.log('✅ OCEAN validation constraints added');
    
    // Add email format validation
    console.log('\n📧 Adding email format validation...');
    
    await prisma.$executeRaw`
      CREATE TRIGGER IF NOT EXISTS validate_email_insert
      BEFORE INSERT ON users
      FOR EACH ROW
      BEGIN
        SELECT CASE 
          WHEN NEW.email IS NOT NULL AND NEW.email NOT LIKE '%@%.%'
          THEN RAISE(ABORT, 'Invalid email format')
        END;
      END
    `;
    
    await prisma.$executeRaw`
      CREATE TRIGGER IF NOT EXISTS validate_email_update
      BEFORE UPDATE ON users
      FOR EACH ROW
      BEGIN
        SELECT CASE 
          WHEN NEW.email IS NOT NULL AND NEW.email NOT LIKE '%@%.%'
          THEN RAISE(ABORT, 'Invalid email format')
        END;
      END
    `;
    
    console.log('✅ Email validation constraints added');
    
    // Add audit action validation
    console.log('\n📝 Adding audit action validation...');
    
    const validActions = ['created', 'updated', 'profile_generated', 'deleted', 'restored'];
    const actionList = validActions.map(action => `'${action}'`).join(', ');
    
    await prisma.$executeRaw`
      CREATE TRIGGER IF NOT EXISTS validate_audit_action_insert
      BEFORE INSERT ON assessment_history
      FOR EACH ROW
      BEGIN
        SELECT CASE 
          WHEN NEW.action NOT IN (${actionList})
          THEN RAISE(ABORT, 'Invalid audit action. Must be one of: ${validActions.join(', ')}')
        END;
      END
    `;
    
    console.log('✅ Audit action validation constraints added');
    
    // Add match percentage validation for career suggestions
    console.log('\n🎯 Adding match percentage validation...');
    
    await prisma.$executeRaw`
      CREATE TRIGGER IF NOT EXISTS validate_match_percentage_insert
      BEFORE INSERT ON career_suggestions
      FOR EACH ROW
      BEGIN
        SELECT CASE 
          WHEN NEW.matchPercentage IS NOT NULL AND (NEW.matchPercentage < 0.0 OR NEW.matchPercentage > 1.0)
          THEN RAISE(ABORT, 'Match percentage must be between 0.0 and 1.0')
        END;
      END
    `;
    
    await prisma.$executeRaw`
      CREATE TRIGGER IF NOT EXISTS validate_match_percentage_update
      BEFORE UPDATE ON career_suggestions
      FOR EACH ROW
      BEGIN
        SELECT CASE 
          WHEN NEW.matchPercentage IS NOT NULL AND (NEW.matchPercentage < 0.0 OR NEW.matchPercentage > 1.0)
          THEN RAISE(ABORT, 'Match percentage must be between 0.0 and 1.0')
        END;
      END
    `;
    
    console.log('✅ Match percentage validation constraints added');
    
    // Add priority validation for development areas
    console.log('\n📊 Adding priority validation...');
    
    const validPriorities = ['high', 'medium', 'low'];
    const priorityList = validPriorities.map(p => `'${p}'`).join(', ');
    
    await prisma.$executeRaw`
      CREATE TRIGGER IF NOT EXISTS validate_priority_insert
      BEFORE INSERT ON development_areas
      FOR EACH ROW
      BEGIN
        SELECT CASE 
          WHEN NEW.priority IS NOT NULL AND NEW.priority NOT IN (${priorityList})
          THEN RAISE(ABORT, 'Priority must be one of: ${validPriorities.join(', ')}')
        END;
      END
    `;
    
    console.log('✅ Priority validation constraints added');
    
    // Test validation constraints
    console.log('\n🧪 Testing validation constraints...');
    await testValidationConstraints(prisma);
    
    console.log('\n🎉 Data validation constraints added successfully!');
    console.log('\n📋 Summary of constraints added:');
    console.log('   ✓ RIASEC scores: 0-30 range validation');
    console.log('   ✓ OCEAN scores: 5-25 range validation');
    console.log('   ✓ Email format validation');
    console.log('   ✓ Audit action validation');
    console.log('   ✓ Match percentage: 0.0-1.0 range validation');
    console.log('   ✓ Priority validation for development areas');
    
  } catch (error) {
    console.error('❌ Failed to add validation constraints:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function testValidationConstraints(prisma) {
  try {
    console.log('   🔍 Testing RIASEC score validation...');
    
    // This should fail - RIASEC score out of range
    try {
      await prisma.$executeRaw`
        INSERT INTO assessments (id, riasecR, riasecI, riasecA, riasecS, riasecE, riasecC, 
                                oceanO, oceanC, oceanE, oceanA, oceanN)
        VALUES ('test-invalid-riasec', 35, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15)
      `;
      console.log('   ❌ RIASEC validation test failed - invalid data was accepted');
    } catch (error) {
      if (error.message.includes('RIASEC R score must be between 0 and 30')) {
        console.log('   ✅ RIASEC validation working correctly');
      } else {
        console.log('   ⚠️  Unexpected error in RIASEC validation:', error.message);
      }
    }
    
    console.log('   🔍 Testing OCEAN score validation...');
    
    // This should fail - OCEAN score out of range
    try {
      await prisma.$executeRaw`
        INSERT INTO assessments (id, riasecR, riasecI, riasecA, riasecS, riasecE, riasecC, 
                                oceanO, oceanC, oceanE, oceanA, oceanN)
        VALUES ('test-invalid-ocean', 15, 15, 15, 15, 15, 15, 30, 15, 15, 15, 15)
      `;
      console.log('   ❌ OCEAN validation test failed - invalid data was accepted');
    } catch (error) {
      if (error.message.includes('OCEAN O score must be between 5 and 25')) {
        console.log('   ✅ OCEAN validation working correctly');
      } else {
        console.log('   ⚠️  Unexpected error in OCEAN validation:', error.message);
      }
    }
    
    console.log('   🔍 Testing email validation...');
    
    // This should fail - invalid email format
    try {
      await prisma.$executeRaw`
        INSERT INTO users (id, email, name)
        VALUES ('test-invalid-email', 'invalid-email', 'Test User')
      `;
      console.log('   ❌ Email validation test failed - invalid email was accepted');
    } catch (error) {
      if (error.message.includes('Invalid email format')) {
        console.log('   ✅ Email validation working correctly');
      } else {
        console.log('   ⚠️  Unexpected error in email validation:', error.message);
      }
    }
    
    console.log('   ✅ All validation tests completed');
    
  } catch (error) {
    console.log('   ⚠️  Error during validation testing:', error.message);
  }
}

// Run if called directly
if (require.main === module) {
  addDataValidationConstraints()
    .then(() => {
      console.log('🎉 Validation constraints setup completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Validation constraints setup failed:', error);
      process.exit(1);
    });
}

module.exports = { addDataValidationConstraints };
