/**
 * Soft Delete Utilities
 * 
 * Provides utility functions for handling soft delete functionality
 * across the application.
 */

export interface SoftDeletable {
  id: string;
  deletedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SoftDeleteOptions {
  includeDeleted?: boolean;
  deletedOnly?: boolean;
}

/**
 * Filter out soft deleted items from an array
 */
export function filterSoftDeleted<T extends SoftDeletable>(
  items: T[],
  options: SoftDeleteOptions = {}
): T[] {
  const { includeDeleted = false, deletedOnly = false } = options;

  if (includeDeleted && !deletedOnly) {
    return items; // Return all items
  }

  if (deletedOnly) {
    return items.filter(item => item.deletedAt !== null);
  }

  // Default: return only non-deleted items
  return items.filter(item => item.deletedAt === null);
}

/**
 * Check if an item is soft deleted
 */
export function isSoftDeleted<T extends SoftDeletable>(item: T): boolean {
  return item.deletedAt !== null;
}

/**
 * Get soft delete status text
 */
export function getSoftDeleteStatus<T extends SoftDeletable>(item: T): string {
  return item.deletedAt ? 'Deleted' : 'Active';
}

/**
 * Get soft delete status color for UI
 */
export function getSoftDeleteStatusColor<T extends SoftDeletable>(item: T): string {
  return item.deletedAt ? 'text-red-600' : 'text-green-600';
}

/**
 * Format soft delete date
 */
export function formatSoftDeleteDate<T extends SoftDeletable>(item: T): string | null {
  if (!item.deletedAt) return null;
  
  return new Intl.DateTimeFormat('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(item.deletedAt));
}

/**
 * Create soft delete filter for Prisma queries
 */
export function createSoftDeleteFilter(options: SoftDeleteOptions = {}) {
  const { includeDeleted = false, deletedOnly = false } = options;

  if (includeDeleted && !deletedOnly) {
    return {}; // No filter - include all
  }

  if (deletedOnly) {
    return { deletedAt: { not: null } };
  }

  // Default: exclude deleted items
  return { deletedAt: null };
}

/**
 * Soft delete confirmation message
 */
export function getSoftDeleteConfirmationMessage(itemType: string, itemName?: string): string {
  const name = itemName ? ` "${itemName}"` : '';
  return `Are you sure you want to delete this ${itemType}${name}? This action can be undone.`;
}

/**
 * Restore confirmation message
 */
export function getRestoreConfirmationMessage(itemType: string, itemName?: string): string {
  const name = itemName ? ` "${itemName}"` : '';
  return `Are you sure you want to restore this ${itemType}${name}?`;
}

/**
 * Permanent delete confirmation message
 */
export function getPermanentDeleteConfirmationMessage(itemType: string, itemName?: string): string {
  const name = itemName ? ` "${itemName}"` : '';
  return `Are you sure you want to permanently delete this ${itemType}${name}? This action cannot be undone!`;
}

/**
 * Soft delete API response type
 */
export interface SoftDeleteResponse {
  success: boolean;
  message: string;
  deletedAt?: string;
  restoredAt?: string;
}

/**
 * Create soft delete success response
 */
export function createSoftDeleteSuccessResponse(
  action: 'deleted' | 'restored',
  itemType: string
): SoftDeleteResponse {
  const timestamp = new Date().toISOString();
  
  if (action === 'deleted') {
    return {
      success: true,
      message: `${itemType} has been successfully deleted`,
      deletedAt: timestamp,
    };
  } else {
    return {
      success: true,
      message: `${itemType} has been successfully restored`,
      restoredAt: timestamp,
    };
  }
}

/**
 * Soft delete error response
 */
export function createSoftDeleteErrorResponse(
  action: 'delete' | 'restore',
  itemType: string,
  error?: string
): SoftDeleteResponse {
  return {
    success: false,
    message: `Failed to ${action} ${itemType}${error ? `: ${error}` : ''}`,
  };
}

/**
 * Bulk soft delete utilities
 */
export interface BulkSoftDeleteOptions {
  ids: string[];
  action: 'delete' | 'restore' | 'permanent_delete';
}

export interface BulkSoftDeleteResult {
  success: boolean;
  processed: number;
  failed: number;
  errors: string[];
}

/**
 * Validate bulk soft delete request
 */
export function validateBulkSoftDeleteRequest(
  options: BulkSoftDeleteOptions
): { valid: boolean; error?: string } {
  if (!options.ids || !Array.isArray(options.ids)) {
    return { valid: false, error: 'IDs array is required' };
  }

  if (options.ids.length === 0) {
    return { valid: false, error: 'At least one ID is required' };
  }

  if (options.ids.length > 100) {
    return { valid: false, error: 'Maximum 100 items can be processed at once' };
  }

  if (!['delete', 'restore', 'permanent_delete'].includes(options.action)) {
    return { valid: false, error: 'Invalid action specified' };
  }

  return { valid: true };
}

/**
 * Create bulk operation confirmation message
 */
export function getBulkOperationConfirmationMessage(
  action: 'delete' | 'restore' | 'permanent_delete',
  count: number,
  itemType: string
): string {
  const plural = count > 1 ? 's' : '';
  
  switch (action) {
    case 'delete':
      return `Are you sure you want to delete ${count} ${itemType}${plural}? This action can be undone.`;
    case 'restore':
      return `Are you sure you want to restore ${count} ${itemType}${plural}?`;
    case 'permanent_delete':
      return `Are you sure you want to permanently delete ${count} ${itemType}${plural}? This action cannot be undone!`;
    default:
      return `Are you sure you want to perform this action on ${count} ${itemType}${plural}?`;
  }
}

/**
 * Soft delete hook for React components
 */
export interface UseSoftDeleteOptions {
  onSuccess?: (action: 'deleted' | 'restored') => void;
  onError?: (error: string) => void;
}

export interface UseSoftDeleteReturn {
  isLoading: boolean;
  softDelete: (id: string) => Promise<void>;
  restore: (id: string) => Promise<void>;
  permanentDelete: (id: string) => Promise<void>;
}

/**
 * Soft delete constants
 */
export const SOFT_DELETE_CONSTANTS = {
  MAX_BULK_OPERATIONS: 100,
  DEFAULT_CACHE_TTL: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  CONFIRMATION_TIMEOUT: 5000, // 5 seconds
  UNDO_TIMEOUT: 10000, // 10 seconds for undo action
} as const;

/**
 * Soft delete permissions
 */
export interface SoftDeletePermissions {
  canDelete: boolean;
  canRestore: boolean;
  canPermanentDelete: boolean;
  canViewDeleted: boolean;
}

/**
 * Check soft delete permissions based on user role
 */
export function checkSoftDeletePermissions(
  userRole: string,
  isOwner: boolean = false
): SoftDeletePermissions {
  const basePermissions: SoftDeletePermissions = {
    canDelete: false,
    canRestore: false,
    canPermanentDelete: false,
    canViewDeleted: false,
  };

  // Admin has all permissions
  if (userRole === 'admin') {
    return {
      canDelete: true,
      canRestore: true,
      canPermanentDelete: true,
      canViewDeleted: true,
    };
  }

  // Owner can delete and restore their own items
  if (isOwner) {
    return {
      canDelete: true,
      canRestore: true,
      canPermanentDelete: false,
      canViewDeleted: true,
    };
  }

  return basePermissions;
}
