const { PrismaClient } = require('../lib/generated/prisma');
const readline = require('readline');

const prisma = new PrismaClient();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Predefined SQL queries
const predefinedQueries = {
  '1': {
    name: 'Show all assessments with basic info',
    sql: `SELECT id, createdAt, riasecR, riasecI, riasecA, riasecS, riasecE, riasecC, 
                 oceanO, oceanC, oceanE, oceanA, oceanN 
          FROM assessments 
          ORDER BY createdAt DESC 
          LIMIT 10`
  },
  '2': {
    name: 'Show assessments with profile status',
    sql: `SELECT a.id, a.createdAt, 
                 CASE WHEN p.id IS NOT NULL THEN 'Yes' ELSE 'No' END as hasProfile,
                 p.profileTitle
          FROM assessments a 
          LEFT JOIN profiles p ON a.id = p.assessmentId 
          ORDER BY a.createdAt DESC`
  },
  '3': {
    name: 'Count assessments by date',
    sql: `SELECT DATE(createdAt) as date, COUNT(*) as count 
          FROM assessments 
          GROUP BY DATE(createdAt) 
          ORDER BY date DESC`
  },
  '4': {
    name: 'Show profiles without assessments (orphaned)',
    sql: `SELECT p.id, p.assessmentId, p.profileTitle 
          FROM profiles p 
          LEFT JOIN assessments a ON p.assessmentId = a.id 
          WHERE a.id IS NULL`
  },
  '5': {
    name: 'Show average scores by type',
    sql: `SELECT 
            AVG(riasecR) as avg_R, AVG(riasecI) as avg_I, AVG(riasecA) as avg_A,
            AVG(riasecS) as avg_S, AVG(riasecE) as avg_E, AVG(riasecC) as avg_C,
            AVG(oceanO) as avg_O, AVG(oceanC) as avg_OC, AVG(oceanE) as avg_OE,
            AVG(oceanA) as avg_OA, AVG(oceanN) as avg_N
          FROM assessments`
  }
};

// Predefined update/delete operations
const predefinedOperations = {
  '1': {
    name: 'Delete assessments older than X days',
    getSQL: async () => {
      const days = await askQuestion('Enter number of days (assessments older than this will be deleted): ');
      return `DELETE FROM assessments WHERE createdAt < datetime('now', '-${days} days')`;
    }
  },
  '2': {
    name: 'Update specific assessment scores',
    getSQL: async () => {
      const id = await askQuestion('Enter assessment ID: ');
      const field = await askQuestion('Enter field to update (riasecR, riasecI, riasecA, riasecS, riasecE, riasecC, oceanO, oceanC, oceanE, oceanA, oceanN): ');
      const value = await askQuestion('Enter new value: ');
      return `UPDATE assessments SET ${field} = ${value}, updatedAt = datetime('now') WHERE id = '${id}'`;
    }
  },
  '3': {
    name: 'Delete specific assessment by ID',
    getSQL: async () => {
      const id = await askQuestion('Enter assessment ID to delete: ');
      return `DELETE FROM assessments WHERE id = '${id}'`;
    }
  },
  '4': {
    name: 'Delete all profiles (keep assessments)',
    getSQL: async () => {
      return `DELETE FROM profiles`;
    }
  }
};

function showQueryMenu() {
  console.log('\n📊 Predefined Queries:');
  console.log('======================');
  Object.entries(predefinedQueries).forEach(([key, query]) => {
    console.log(`${key}. ${query.name}`);
  });
  console.log('0. Back to main menu');
}

function showOperationMenu() {
  console.log('\n⚠️  Predefined Operations (DANGEROUS):');
  console.log('======================================');
  Object.entries(predefinedOperations).forEach(([key, op]) => {
    console.log(`${key}. ${op.name}`);
  });
  console.log('0. Back to main menu');
}

function showMainMenu() {
  console.log('\n🔧 SQL Runner - Direct Database Access');
  console.log('=====================================');
  console.log('1. 📊 Run predefined queries (SELECT)');
  console.log('2. ⚠️  Run predefined operations (UPDATE/DELETE)');
  console.log('3. 💻 Run custom SQL query');
  console.log('4. 📋 Show database schema');
  console.log('0. 🚪 Exit');
  console.log('=====================================');
}

async function runPredefinedQuery() {
  while (true) {
    showQueryMenu();
    const choice = await askQuestion('\nSelect a query: ');
    
    if (choice === '0') break;
    
    const query = predefinedQueries[choice];
    if (!query) {
      console.log('❌ Invalid choice');
      continue;
    }
    
    try {
      console.log(`\n🔍 Running: ${query.name}`);
      console.log(`SQL: ${query.sql}`);
      console.log('='.repeat(80));
      
      const result = await prisma.$queryRawUnsafe(query.sql);
      
      if (Array.isArray(result) && result.length > 0) {
        console.table(result);
      } else {
        console.log('📭 No results found');
      }
      
    } catch (error) {
      console.error('❌ Query error:', error.message);
    }
    
    await askQuestion('\nPress Enter to continue...');
  }
}

async function runPredefinedOperation() {
  while (true) {
    showOperationMenu();
    const choice = await askQuestion('\nSelect an operation: ');
    
    if (choice === '0') break;
    
    const operation = predefinedOperations[choice];
    if (!operation) {
      console.log('❌ Invalid choice');
      continue;
    }
    
    try {
      console.log(`\n⚠️  Operation: ${operation.name}`);
      const sql = await operation.getSQL();
      
      console.log(`SQL: ${sql}`);
      const confirm = await askQuestion('\n⚠️  This operation will modify your database. Type "CONFIRM" to proceed: ');
      
      if (confirm !== 'CONFIRM') {
        console.log('❌ Operation cancelled');
        continue;
      }
      
      const result = await prisma.$executeRawUnsafe(sql);
      console.log(`✅ Operation completed. Affected rows: ${result}`);
      
    } catch (error) {
      console.error('❌ Operation error:', error.message);
    }
    
    await askQuestion('\nPress Enter to continue...');
  }
}

async function runCustomSQL() {
  console.log('\n💻 Custom SQL Query');
  console.log('===================');
  console.log('⚠️  WARNING: Be careful with UPDATE/DELETE operations!');
  console.log('💡 Tip: Use SELECT queries to explore data safely');
  
  const sql = await askQuestion('\nEnter your SQL query: ');
  
  if (!sql) {
    console.log('❌ Empty query');
    return;
  }
  
  try {
    // Check if it's a potentially dangerous operation
    const isDangerous = /^\s*(UPDATE|DELETE|DROP|ALTER|INSERT)\s+/i.test(sql);
    
    if (isDangerous) {
      console.log('⚠️  This appears to be a data-modifying operation!');
      const confirm = await askQuestion('Type "CONFIRM" to proceed: ');
      if (confirm !== 'CONFIRM') {
        console.log('❌ Query cancelled');
        return;
      }
    }
    
    console.log('\n🔍 Executing query...');
    console.log('='.repeat(80));
    
    if (isDangerous) {
      const result = await prisma.$executeRawUnsafe(sql);
      console.log(`✅ Query executed. Affected rows: ${result}`);
    } else {
      const result = await prisma.$queryRawUnsafe(sql);
      
      if (Array.isArray(result) && result.length > 0) {
        console.table(result);
      } else {
        console.log('📭 No results found');
      }
    }
    
  } catch (error) {
    console.error('❌ SQL error:', error.message);
  }
}

async function showSchema() {
  console.log('\n📋 Database Schema:');
  console.log('==================');
  
  try {
    // Show tables
    const tables = await prisma.$queryRaw`
      SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
    `;
    
    console.log('📊 Tables:');
    tables.forEach(table => console.log(`  - ${table.name}`));
    
    // Show schema for each table
    for (const table of tables) {
      if (table.name.startsWith('_')) continue; // Skip system tables
      
      console.log(`\n📋 ${table.name} structure:`);
      const schema = await prisma.$queryRawUnsafe(`PRAGMA table_info(${table.name})`);
      console.table(schema);
    }
    
  } catch (error) {
    console.error('❌ Error showing schema:', error.message);
  }
}

async function main() {
  console.log('🚀 Starting SQL Runner...');
  console.log('⚠️  WARNING: This tool provides direct database access. Use with caution!');
  
  try {
    while (true) {
      showMainMenu();
      const choice = await askQuestion('\nSelect an option: ');
      
      switch (choice) {
        case '1':
          await runPredefinedQuery();
          break;
        case '2':
          await runPredefinedOperation();
          break;
        case '3':
          await runCustomSQL();
          break;
        case '4':
          await showSchema();
          await askQuestion('\nPress Enter to continue...');
          break;
        case '0':
          console.log('👋 Goodbye!');
          return;
        default:
          console.log('❌ Invalid option');
      }
    }
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
    rl.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}
